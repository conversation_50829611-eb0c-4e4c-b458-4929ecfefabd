import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Redirect, Stack, useSegments } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuthStore } from "@/stores/auth-store";
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export const unstable_settings = {
  initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (error) {
      console.error("Font loading error:", error);
      // Don't throw error, just log it and continue
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  const segments = useSegments();
  const { isAuthenticated, setIsAuthenticated, user, migrateToMultiUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is authenticated on app load
    const checkAuth = async () => {
      try {
        const authData = await AsyncStorage.getItem("salon-copilot-auth");
        if (authData) {
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [setIsAuthenticated]);

  // Migrate existing users to multi-user system
  useEffect(() => {
    if (user && !user.id) {
      migrateToMultiUser();
    }
  }, [user, migrateToMultiUser]);

  // Show loading state
  if (isLoading) {
    return null;
  }

  // Check if user is in special routes
  const inAuthGroup = segments[0] === "auth";
  const inOnboardingGroup = segments[0] === "onboarding";
  
  // If the user is not authenticated and we're not in auth or onboarding, redirect to auth
  if (!isAuthenticated && !inAuthGroup && !inOnboardingGroup) {
    return <Redirect href="/auth/login" />;
  }

  // If the user is authenticated and we're in the auth group, redirect to home
  if (isAuthenticated && inAuthGroup) {
    return <Redirect href="/(tabs)" />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Stack
        screenOptions={{
          headerBackTitle: "Atrás",
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="onboarding" options={{ headerShown: false }} />
        <Stack.Screen name="client/[id]" options={{ headerShown: false, title: "Detalle del Cliente" }} />
        <Stack.Screen name="client/new" options={{ headerShown: false, title: "Nuevo Cliente" }} />
        <Stack.Screen name="service/client-selection" options={{ headerShown: false, title: "Seleccionar Cliente" }} />
        <Stack.Screen name="service/safety-verification" options={{ headerShown: false, title: "Verificación de Seguridad" }} />
        <Stack.Screen name="service/new" options={{ headerShown: false, title: "Nuevo Servicio" }} />
        <Stack.Screen 
          name="service/desired-camera" 
          options={{ 
            headerShown: false, 
            title: "Captura de Foto",
            unmountOnBlur: true,
            animation: 'slide_from_right'
          }} 
        />
        <Stack.Screen name="inventory/new" options={{ headerShown: false, title: "Añadir Producto" }} />
        <Stack.Screen name="inventory/[id]" options={{ headerShown: false, title: "Detalle del Producto" }} />
        <Stack.Screen name="modal" options={{ presentation: "modal" }} />
      </Stack>
    </GestureHandlerRootView>
  );
}