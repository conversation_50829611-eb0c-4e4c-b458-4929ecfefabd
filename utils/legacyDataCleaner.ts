import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Utility to clean all legacy data from the application
 * This includes brand preferences with string names instead of UUIDs
 */
export class LegacyDataCleaner {
  
  /**
   * Clean all legacy brand data from AsyncStorage
   */
  static async cleanAllLegacyData(): Promise<void> {
    try {
      console.log('🧹 Starting comprehensive legacy data cleanup...');
      
      // Get all AsyncStorage keys
      const allKeys = await AsyncStorage.getAllKeys();
      console.log('📋 Found AsyncStorage keys:', allKeys);
      
      // Keys that might contain legacy brand data
      const keysToCheck = [
        'auth-storage',
        'salon-config-storage',
        'team-storage',
        'salonier-client-history',
        'inventory-storage',
        'formulation-storage'
      ];
      
      for (const key of keysToCheck) {
        if (allKeys.includes(key)) {
          await this.cleanStorageKey(key);
        }
      }
      
      console.log('✅ Legacy data cleanup completed');
    } catch (error) {
      console.error('❌ Error during legacy data cleanup:', error);
    }
  }
  
  /**
   * Clean a specific AsyncStorage key
   */
  private static async cleanStorageKey(key: string): Promise<void> {
    try {
      console.log(`🔍 Checking storage key: ${key}`);
      
      const data = await AsyncStorage.getItem(key);
      if (!data) {
        console.log(`  ℹ️ No data found for key: ${key}`);
        return;
      }
      
      const parsedData = JSON.parse(data);
      let hasLegacyData = false;
      let cleanedData = { ...parsedData };
      
      // Check for legacy brand preferences in various locations
      if (this.hasLegacyBrandData(parsedData)) {
        console.log(`  🗑️ Found legacy brand data in: ${key}`);
        cleanedData = this.removeLegacyBrandData(parsedData);
        hasLegacyData = true;
      }
      
      if (hasLegacyData) {
        await AsyncStorage.setItem(key, JSON.stringify(cleanedData));
        console.log(`  ✅ Cleaned legacy data from: ${key}`);
      } else {
        console.log(`  ✅ No legacy data found in: ${key}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error cleaning storage key ${key}:`, error);
    }
  }
  
  /**
   * Check if data contains legacy brand information
   */
  private static hasLegacyBrandData(data: any): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    // Check various possible locations for brand data
    const checkPaths = [
      'state.preferredBrandLines',
      'preferredBrandLines',
      'state.configuration.preferredBrands',
      'configuration.preferredBrands',
      'state.brandSelections',
      'brandSelections'
    ];
    
    for (const path of checkPaths) {
      const value = this.getNestedValue(data, path);
      if (Array.isArray(value)) {
        const hasLegacy = value.some((item: any) => {
          if (typeof item === 'object' && item.brandId) {
            return !uuidRegex.test(item.brandId);
          }
          if (typeof item === 'string') {
            return !uuidRegex.test(item);
          }
          return false;
        });
        
        if (hasLegacy) {
          console.log(`  🔍 Found legacy brand data at path: ${path}`, value);
          return true;
        }
      }
    }
    
    return false;
  }
  
  /**
   * Remove legacy brand data from the data object
   */
  private static removeLegacyBrandData(data: any): any {
    const cleanedData = JSON.parse(JSON.stringify(data)); // Deep clone
    
    // Paths to clean
    const cleanPaths = [
      'state.preferredBrandLines',
      'preferredBrandLines',
      'state.configuration.preferredBrands',
      'configuration.preferredBrands',
      'state.brandSelections',
      'brandSelections'
    ];
    
    for (const path of cleanPaths) {
      this.setNestedValue(cleanedData, path, []);
    }
    
    return cleanedData;
  }
  
  /**
   * Get nested value from object using dot notation
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }
  
  /**
   * Set nested value in object using dot notation
   */
  private static setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    if (!lastKey) return;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    if (target && typeof target === 'object') {
      target[lastKey] = value;
    }
  }
  
  /**
   * Force clear all AsyncStorage (nuclear option)
   */
  static async clearAllAsyncStorage(): Promise<void> {
    try {
      console.log('💥 NUCLEAR OPTION: Clearing ALL AsyncStorage data...');
      await AsyncStorage.clear();
      console.log('✅ All AsyncStorage data cleared');
    } catch (error) {
      console.error('❌ Error clearing AsyncStorage:', error);
    }
  }
  
  /**
   * List all AsyncStorage contents for debugging
   */
  static async debugAsyncStorage(): Promise<void> {
    try {
      console.log('🔍 DEBUG: AsyncStorage contents:');
      const keys = await AsyncStorage.getAllKeys();
      
      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        console.log(`  ${key}:`, value ? JSON.parse(value) : null);
      }
    } catch (error) {
      console.error('❌ Error debugging AsyncStorage:', error);
    }
  }
}

export default LegacyDataCleaner;
