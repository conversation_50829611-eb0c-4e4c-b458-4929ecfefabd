import { Permission } from './permissions';

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: '<PERSON><PERSON>' | 'Asistente' | 'Estilista' | 'Recepcionista' | 'Manager';
  phone?: string;
  licenseNumber?: string;
  specializations?: string[];
  status: 'active' | 'inactive';
  joinedDate: string;
  avatar?: string;
  passwordHash?: string;
  isOwner: boolean;
  permissions?: Permission[];
  salonId: string;
}

export interface TeamStore {
  members: TeamMember[];
  addMember: (member: Omit<TeamMember, 'id' | 'joinedDate'>) => void;
  updateMember: (id: string, updates: Partial<TeamMember>) => void;
  removeMember: (id: string) => void;
  toggleMemberStatus: (id: string) => void;
  getMemberByEmail: (email: string) => TeamMember | undefined;
  getMembersBySalon: (salonId: string) => TeamMember[];
  verifyPassword: (email: string, password: string) => Promise<boolean>;
  hashPassword: (password: string) => Promise<string>;
  updatePassword: (memberId: string, newPasswordHash: string) => void;
}