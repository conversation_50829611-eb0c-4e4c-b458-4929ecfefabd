# Configuración de Supabase para Salonier

Esta guía te ayudará a configurar Supabase para la aplicación Salonier, incluyendo la base de datos, autenticación y servicios de IA.

## 📋 Requisitos Previos

- Cuenta de Supabase (gratuita en [supabase.com](https://supabase.com))
- Node.js instalado
- Proyecto Salonier clonado

## 🚀 Configuración Inicial

### 1. Crear Proyecto en Supabase

1. Ve a [supabase.com](https://supabase.com) y crea una cuenta
2. Crea un nuevo proyecto llamado "Salonier"
3. Selecciona la región más cercana (recomendado: eu-west-3)
4. Espera a que el proyecto se inicialice

### 2. Configurar Variables de Entorno

1. Copia el archivo `.env.example` a `.env`:
   ```bash
   cp .env.example .env
   ```

2. Ve a tu proyecto de Supabase > Settings > API
3. Copia las credenciales y actualiza el archivo `.env`:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=https://tu-proyecto.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_aqui
   ```

### 3. Configurar Base de Datos

La base de datos ya está configurada automáticamente con todas las tablas, funciones y políticas necesarias.

#### Tablas Principales:
- **salons**: Información de salones
- **profiles**: Perfiles de usuarios (extiende auth.users)
- **clients**: Clientes del salón
- **brands**: Marcas de productos
- **products**: Productos de coloración
- **salon_inventory**: Inventario por salón
- **hair_analyses**: Análisis de cabello con IA
- **formulations**: Formulaciones de color
- **ai_service_configs**: Configuración de servicios de IA

#### Funciones de Base de Datos:
- `get_user_salon_id()`: Obtiene el ID del salón del usuario
- `user_has_permission()`: Verifica permisos de usuario
- `validate_operation_permission()`: Valida operaciones sensibles
- `handle_new_user()`: Crea perfil automáticamente al registrarse

### 4. Configurar Autenticación

1. Ve a Authentication > Settings en tu proyecto de Supabase
2. Configura las siguientes opciones:
   - **Enable email confirmations**: Activado (recomendado)
   - **Enable phone confirmations**: Opcional
   - **Site URL**: Tu dominio de producción
   - **Redirect URLs**: Agrega las URLs de desarrollo y producción

### 5. Configurar Row Level Security (RLS)

Las políticas de RLS ya están configuradas automáticamente para:
- Proteger datos por salón
- Permitir acceso solo a usuarios autorizados
- Separar datos entre diferentes salones
- Proteger configuraciones sensibles de IA

## 🧪 Verificar Configuración

Ejecuta el script de verificación:

```bash
npm run test:supabase
```

Este script verificará:
- ✅ Conexión a Supabase
- ✅ Acceso a todas las tablas
- ✅ Funcionamiento de las funciones de base de datos
- ✅ Sistema de autenticación
- ✅ Políticas de seguridad (RLS)

## 🤖 Configurar Servicios de IA

### OpenAI (Recomendado)

1. Obtén una API key de [OpenAI](https://platform.openai.com/api-keys)
2. En la aplicación, ve a Configuración > Servicios de IA
3. Agrega tu API key de OpenAI
4. La aplicación usará GPT-4 Vision para análisis de cabello

### Configuración Adicional

Para servicios adicionales como Perplexity o Google Vision:
1. Obtén las API keys correspondientes
2. Configúralas en la sección de Servicios de IA
3. Las claves se almacenan encriptadas en Supabase

## 🔧 Comandos Útiles

```bash
# Verificar conexión a Supabase
npm run test:supabase

# Ejecutar tests de servicios
npm run test:services

# Ejecutar tests de integración
npm run test:integration

# Ejecutar todos los tests
npm run test:all

# Iniciar aplicación
npm start
```

## 📊 Estructura de Datos

### Usuarios y Salones
```
auth.users (Supabase Auth)
├── profiles (perfil extendido)
└── salons (información del salón)
    ├── clients (clientes del salón)
    ├── salon_inventory (inventario)
    ├── hair_analyses (análisis de IA)
    └── formulations (formulaciones)
```

### Productos
```
brands (marcas)
├── product_lines (líneas de productos)
└── products (productos individuales)
    └── salon_inventory (stock por salón)
```

## 🔒 Seguridad

### Políticas Implementadas:
- **Aislamiento por salón**: Los usuarios solo ven datos de su salón
- **Permisos granulares**: Sistema de permisos por funcionalidad
- **Encriptación**: Datos sensibles como API keys están encriptados
- **Auditoría**: Todas las acciones importantes se registran
- **Rate limiting**: Protección contra abuso de APIs

### Permisos Disponibles:
- `manage_clients`: Gestionar clientes
- `manage_inventory`: Gestionar inventario
- `view_all_formulations`: Ver todas las formulaciones
- `view_all_analyses`: Ver todos los análisis
- `view_reports`: Ver reportes
- `manage_team`: Gestionar equipo

## 🚨 Solución de Problemas

### Error de Conexión
```bash
# Verificar variables de entorno
echo $EXPO_PUBLIC_SUPABASE_URL
echo $EXPO_PUBLIC_SUPABASE_ANON_KEY

# Verificar conectividad
npm run test:supabase
```

### Error de Permisos
- Verifica que el usuario tenga un perfil creado
- Confirma que el usuario esté asignado a un salón
- Revisa los permisos del usuario en la tabla `profiles`

### Error de RLS
- Las políticas de RLS protegen los datos
- Solo los propietarios pueden acceder a configuraciones de IA
- Los usuarios solo ven datos de su salón

## 📞 Soporte

Si encuentras problemas:
1. Revisa los logs en la consola del navegador
2. Ejecuta `npm run test:supabase` para diagnosticar
3. Verifica la configuración en Supabase Dashboard
4. Consulta la documentación de [Supabase](https://supabase.com/docs)

## 🔄 Actualizaciones

Para futuras actualizaciones de la base de datos:
1. Las migraciones se aplicarán automáticamente
2. Siempre haz backup antes de actualizaciones importantes
3. Usa el entorno de desarrollo para probar cambios

---

¡Tu aplicación Salonier ya está lista para usar con Supabase! 🎉
