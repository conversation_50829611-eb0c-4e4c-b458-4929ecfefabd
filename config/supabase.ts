import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://bvniobhkvofmfcabfmzg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2bmlvYmhrdm9mbWZjYWJmbXpnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNDMwNDksImV4cCI6MjA2NzcxOTA0OX0.4Jt58oFPaxBssCXBdYYnknpO_DYcgiEffWjiOyGCtVw';

// Create Supabase client
console.log('🔧 Creating Supabase client...');
console.log('📍 URL:', supabaseUrl);
console.log('🔑 Key:', supabaseAnonKey ? 'Present' : 'Missing');

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Enable automatic session refresh
    autoRefreshToken: true,
    // Persist session in local storage
    persistSession: true,
    // Detect session from URL (useful for email confirmations)
    detectSessionInUrl: false,
  },
});

console.log('✅ Supabase client created successfully');

// Database types (will be generated from Supabase later)
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          name: string;
          is_owner: boolean;
          salon_id: string;
          permissions: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          is_owner?: boolean;
          salon_id: string;
          permissions?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          is_owner?: boolean;
          salon_id?: string;
          permissions?: string[] | null;
          updated_at?: string;
        };
      };
      salons: {
        Row: {
          id: string;
          name: string;
          address: string | null;
          phone: string | null;
          email: string | null;
          owner_id: string;
          settings: any | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address?: string | null;
          phone?: string | null;
          email?: string | null;
          owner_id: string;
          settings?: any | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string | null;
          phone?: string | null;
          email?: string | null;
          owner_id?: string;
          settings?: any | null;
          updated_at?: string;
        };
      };
      clients: {
        Row: {
          id: string;
          salon_id: string;
          name: string;
          email: string | null;
          phone: string | null;
          notes: string | null;
          last_visit: string | null;
          risk_level: string | null;
          allergies: string[] | null;
          preferences: any | null;
          known_allergies: string | null;
          pregnancy_or_nursing: boolean | null;
          sensitive_skin: boolean | null;
          chemical_treatments: any | null;
          accepts_reminders: boolean | null;
          preferred_contact: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          salon_id: string;
          name: string;
          email?: string | null;
          phone?: string | null;
          notes?: string | null;
          last_visit?: string | null;
          risk_level?: string | null;
          allergies?: string[] | null;
          preferences?: any | null;
          known_allergies?: string | null;
          pregnancy_or_nursing?: boolean | null;
          sensitive_skin?: boolean | null;
          chemical_treatments?: any | null;
          accepts_reminders?: boolean | null;
          preferred_contact?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          salon_id?: string;
          name?: string;
          email?: string | null;
          phone?: string | null;
          notes?: string | null;
          last_visit?: string | null;
          risk_level?: string | null;
          allergies?: string[] | null;
          preferences?: any | null;
          known_allergies?: string | null;
          pregnancy_or_nursing?: boolean | null;
          sensitive_skin?: boolean | null;
          chemical_treatments?: any | null;
          accepts_reminders?: boolean | null;
          preferred_contact?: string | null;
          updated_at?: string;
        };
      };
      // Add more tables as needed
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
