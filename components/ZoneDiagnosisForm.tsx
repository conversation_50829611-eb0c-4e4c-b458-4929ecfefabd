import React from "react";
import { StyleSheet, Text, View, TextInput, Switch } from "react-native";
import DiagnosisSelector from "./DiagnosisSelector";
import Colors from "@/constants/colors";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import {
  HairZone,
  HairState,
  NaturalTone,
  Undertone,
  UnwantedTone,
  HairPorosity,
  HairElasticity,
  HairResistance,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  GrayHairType,
  GrayPattern,
  CuticleState,
  getNaturalToneOptions,
  getUndertoneOptions,
  getUnwantedToneOptions,
  getHairStateOptions,
  getHairPorosityOptions,
  getHairElasticityOptions,
  getHairResistanceOptions,
  getDepthLevels,
  getGrayHairTypeOptions,
  getGrayPatternOptions,
  getCuticleStateOptions
} from "@/types/hair-diagnosis";

interface ZoneDiagnosisFormProps {
  zone: HairZone;
  colorAnalysis: Partial<ZoneColorAnalysis>;
  physicalAnalysis: Partial<ZonePhysicalAnalysis>;
  onColorChange: (analysis: Partial<ZoneColorAnalysis>) => void;
  onPhysicalChange: (analysis: Partial<ZonePhysicalAnalysis>) => void;
  isFromAI?: boolean;
}

export default function ZoneDiagnosisForm({
  zone,
  colorAnalysis,
  physicalAnalysis,
  onColorChange,
  onPhysicalChange,
  isFromAI = false
}: ZoneDiagnosisFormProps) {
  const damageOptions = ['Bajo', 'Medio', 'Alto'];
  const { configuration } = useSalonConfigStore();
  
  // Initialize demarkation state from existing data
  const existingBand = colorAnalysis.demarkationBands?.[0];
  const [showDemarkationBand, setShowDemarkationBand] = React.useState(!!existingBand);
  const [demarkationDistance, setDemarkationDistance] = React.useState(() => {
    if (existingBand) {
      // Convert from cm to display unit if needed
      const distance = configuration.measurementSystem === 'imperial' 
        ? (existingBand.location / 2.54).toFixed(1)
        : existingBand.location.toString();
      return distance;
    }
    return "";
  });
  const [demarkationContrast, setDemarkationContrast] = React.useState<"Bajo" | "Medio" | "Alto">(
    existingBand?.contrast as "Bajo" | "Medio" | "Alto" || "Medio"
  );
  
  // Helper para obtener la unidad de medida
  const getDistanceUnit = () => configuration.measurementSystem === 'metric' ? 'cm' : 'inches';
  
  // Helper para convertir entre unidades
  const convertDistance = (value: number, from: 'cm' | 'inch', to: 'cm' | 'inch') => {
    if (from === to) return value;
    return from === 'cm' ? value / 2.54 : value * 2.54;
  };
  
  // Debug log
  React.useEffect(() => {
    console.log(`ZoneDiagnosisForm [${zone}] - colorAnalysis:`, colorAnalysis);
    console.log(`ZoneDiagnosisForm [${zone}] - physicalAnalysis:`, physicalAnalysis);
  }, [zone, colorAnalysis, physicalAnalysis]);

  return (
    <View style={styles.container}>
      <Text style={styles.zoneTitle}>{zone}</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Análisis de Color</Text>
        
        <DiagnosisSelector
          label="Nivel de profundidad"
          value={colorAnalysis.depthLevel?.toString() || ""}
          options={getDepthLevels()}
          onValueChange={(value) => onColorChange({ ...colorAnalysis, depthLevel: parseFloat(value) })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Tono base"
          value={colorAnalysis.tone || ""}
          options={getNaturalToneOptions()}
          onValueChange={(value) => onColorChange({ ...colorAnalysis, tone: value as any })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Subtono/Reflejo"
          value={colorAnalysis.undertone || ""}
          options={getUndertoneOptions()}
          onValueChange={(value) => onColorChange({ ...colorAnalysis, undertone: value as any })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Estado del cabello"
          value={colorAnalysis.state || ""}
          options={getHairStateOptions()}
          onValueChange={(value) => onColorChange({ ...colorAnalysis, state: value as any })}
          required
          isFromAI={isFromAI}
        />

        {zone === HairZone.ROOTS && (
          <>
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Porcentaje de canas <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                style={styles.input}
                value={colorAnalysis.grayPercentage?.toString() || ""}
                onChangeText={(value) => onColorChange({ 
                  ...colorAnalysis, 
                  grayPercentage: value ? parseInt(value) : undefined 
                })}
                placeholder="0-100"
                keyboardType="numeric"
                maxLength={3}
              />
            </View>

            {/* Campos avanzados de canas - solo si hay canas presentes */}
            {colorAnalysis.grayPercentage && colorAnalysis.grayPercentage > 0 && (
              <>
                <DiagnosisSelector
                  label="Tipo de cana"
                  value={colorAnalysis.grayType || ""}
                  options={getGrayHairTypeOptions()}
                  onValueChange={(value) => onColorChange({ 
                    ...colorAnalysis, 
                    grayType: value as GrayHairType 
                  })}
                  isFromAI={isFromAI}
                />

                <DiagnosisSelector
                  label="Patrón de distribución"
                  value={colorAnalysis.grayPattern || ""}
                  options={getGrayPatternOptions()}
                  onValueChange={(value) => onColorChange({ 
                    ...colorAnalysis, 
                    grayPattern: value as GrayPattern 
                  })}
                  isFromAI={isFromAI}
                />
              </>
            )}
          </>
        )}

        {colorAnalysis.state && colorAnalysis.state !== HairState.NATURAL && (
          <>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Proceso químico previo</Text>
              <TextInput
                style={styles.input}
                value={colorAnalysis.previousChemical || ""}
                onChangeText={(value) => onColorChange({ ...colorAnalysis, previousChemical: value })}
                placeholder="Ej: Decoloración, Tinte permanente..."
              />
            </View>
            
            <DiagnosisSelector
              label="Matiz no deseado presente"
              value={colorAnalysis.unwantedTone || ""}
              options={["Ninguno", ...getUnwantedToneOptions()]}
              onValueChange={(value) => onColorChange({ 
                ...colorAnalysis, 
                unwantedTone: value === "Ninguno" ? undefined : value as UnwantedTone
              })}
              isFromAI={isFromAI}
            />

            <DiagnosisSelector
              label="Acumulación de pigmentos"
              value={colorAnalysis.pigmentAccumulation || ""}
              options={["Ninguna", "Baja", "Media", "Alta"]}
              onValueChange={(value) => onColorChange({ 
                ...colorAnalysis, 
                pigmentAccumulation: value === "Ninguna" ? undefined : value as "Baja" | "Media" | "Alta"
              })}
              isFromAI={isFromAI}
            />
          </>
        )}

        {/* Estado de cutícula - disponible para todas las zonas */}
        <DiagnosisSelector
          label="Estado de la cutícula"
          value={colorAnalysis.cuticleState || ""}
          options={getCuticleStateOptions()}
          onValueChange={(value) => onColorChange({ 
            ...colorAnalysis, 
            cuticleState: value as CuticleState
          })}
          isFromAI={isFromAI}
        />

        {/* Bandas de demarcación - solo para cabello con proceso químico */}
        {colorAnalysis.state && colorAnalysis.state !== HairState.NATURAL && (
          <View style={styles.formGroup}>
            <View style={styles.switchContainer}>
              <Text style={styles.label}>¿Presenta bandas de demarcación?</Text>
              <Switch
                value={showDemarkationBand}
                onValueChange={(value) => {
                  setShowDemarkationBand(value);
                  if (!value) {
                    // Limpiar datos si se desactiva
                    onColorChange({
                      ...colorAnalysis,
                      demarkationBands: undefined
                    });
                  }
                }}
                trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                thumbColor={showDemarkationBand ? Colors.light.surface : Colors.light.textSecondary}
              />
            </View>

            {showDemarkationBand && (
              <>
                <View style={styles.formGroup}>
                  <Text style={styles.label}>
                    Distancia desde la raíz ({getDistanceUnit()})
                  </Text>
                  <TextInput
                    style={styles.input}
                    value={demarkationDistance}
                    onChangeText={(value) => {
                      setDemarkationDistance(value);
                      const numValue = parseFloat(value);
                      if (!isNaN(numValue)) {
                        // Convertir a cm para almacenamiento
                        const cmValue = configuration.measurementSystem === 'imperial' 
                          ? convertDistance(numValue, 'inch', 'cm')
                          : numValue;
                        
                        onColorChange({
                          ...colorAnalysis,
                          demarkationBands: [{
                            location: cmValue,
                            contrast: demarkationContrast
                          }]
                        });
                      }
                    }}
                    placeholder={`0.0 ${getDistanceUnit()}`}
                    keyboardType="decimal-pad"
                  />
                </View>

                <DiagnosisSelector
                  label="Contraste de la banda"
                  value={demarkationContrast}
                  options={["Bajo", "Medio", "Alto"]}
                  onValueChange={(value) => {
                    const contrast = value as "Bajo" | "Medio" | "Alto";
                    setDemarkationContrast(contrast);
                    if (demarkationDistance) {
                      const numValue = parseFloat(demarkationDistance);
                      if (!isNaN(numValue)) {
                        const cmValue = configuration.measurementSystem === 'imperial' 
                          ? convertDistance(numValue, 'inch', 'cm')
                          : numValue;
                        
                        onColorChange({
                          ...colorAnalysis,
                          demarkationBands: [{
                            location: cmValue,
                            contrast: contrast
                          }]
                        });
                      }
                    }
                  }}
                  isFromAI={isFromAI}
                />
              </>
            )}
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Características Físicas</Text>
        
        <DiagnosisSelector
          label="Porosidad"
          value={physicalAnalysis.porosity || ""}
          options={getHairPorosityOptions()}
          onValueChange={(value) => onPhysicalChange({ ...physicalAnalysis, porosity: value as any })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Elasticidad"
          value={physicalAnalysis.elasticity || ""}
          options={getHairElasticityOptions()}
          onValueChange={(value) => onPhysicalChange({ ...physicalAnalysis, elasticity: value as any })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Resistencia"
          value={physicalAnalysis.resistance || ""}
          options={getHairResistanceOptions()}
          onValueChange={(value) => onPhysicalChange({ ...physicalAnalysis, resistance: value as any })}
          required
          isFromAI={isFromAI}
        />

        <DiagnosisSelector
          label="Nivel de daño"
          value={physicalAnalysis.damage || ""}
          options={damageOptions}
          onValueChange={(value) => onPhysicalChange({ ...physicalAnalysis, damage: value as 'Bajo' | 'Medio' | 'Alto' })}
          required
          isFromAI={isFromAI}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  zoneTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.light.primary,
    marginBottom: 20,
    textAlign: "center",
    backgroundColor: Colors.light.primary + "10",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignSelf: "center",
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
  },
  required: {
    color: Colors.light.error,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
});