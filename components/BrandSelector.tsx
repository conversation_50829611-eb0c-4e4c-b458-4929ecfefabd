import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { brandService, BrandWithLines } from '@/services/brandService';
import { useAuthStore } from '@/stores/auth-store';

interface BrandSelectorProps {
  onBrandSelect?: (brandId: string, brandName: string) => void;
  selectedBrands?: string[];
}

export default function BrandSelector({ onBrandSelect, selectedBrands = [] }: BrandSelectorProps) {
  const [brands, setBrands] = useState<BrandWithLines[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { addBrandLineSelection } = useAuthStore();

  useEffect(() => {
    loadBrands();
  }, []);

  const loadBrands = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await brandService.getBrandsWithLines();
      
      if (error) {
        setError(error.message);
        console.error('Error loading brands:', error);
      } else {
        setBrands(data || []);
        console.log('Loaded brands:', data);
      }
    } catch (err) {
      setError('Failed to load brands');
      console.error('Error loading brands:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBrandSelect = async (brand: BrandWithLines) => {
    try {
      console.log('Selected brand:', brand.name, 'ID:', brand.id);
      
      // Get all product line IDs for this brand
      const lineIds = brand.product_lines?.map(line => line.id) || [];
      
      // Add to auth store
      await addBrandLineSelection(brand.id, lineIds);
      
      // Call parent callback
      onBrandSelect?.(brand.id, brand.name);
      
      console.log('Brand selection added successfully');
    } catch (error) {
      console.error('Error selecting brand:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading brands...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadBrands}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Available Brands</Text>
      <ScrollView style={styles.scrollView}>
        {brands.map((brand) => (
          <TouchableOpacity
            key={brand.id}
            style={[
              styles.brandCard,
              selectedBrands.includes(brand.id) && styles.selectedBrand
            ]}
            onPress={() => handleBrandSelect(brand)}
          >
            <View style={styles.brandHeader}>
              <Text style={styles.brandName}>{brand.name}</Text>
              <Text style={styles.brandId}>ID: {brand.id.substring(0, 8)}...</Text>
            </View>
            
            {brand.description && (
              <Text style={styles.brandDescription}>{brand.description}</Text>
            )}
            
            {brand.product_lines && brand.product_lines.length > 0 && (
              <View style={styles.productLinesContainer}>
                <Text style={styles.productLinesTitle}>Product Lines:</Text>
                {brand.product_lines.map((line) => (
                  <Text key={line.id} style={styles.productLine}>
                    • {line.name} ({line.line_type})
                  </Text>
                ))}
              </View>
            )}
            
            {selectedBrands.includes(brand.id) && (
              <Text style={styles.selectedText}>✓ Selected</Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {brands.length === 0 && (
        <Text style={styles.noBrandsText}>No brands available</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  brandCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedBrand: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  brandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  brandName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  brandId: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  brandDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  productLinesContainer: {
    marginTop: 8,
  },
  productLinesTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  productLine: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  selectedText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'right',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  noBrandsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 20,
  },
});
