import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { supabase } from '@/config/supabase';

export default function SupabaseTest() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      console.log('🧪 Testing Supabase connection...');
      
      // Simple test query
      const { data, error } = await supabase
        .from('brands')
        .select('count')
        .limit(1);

      if (error) {
        console.error('❌ Supabase connection error:', error);
        setConnectionStatus('error');
        setError(error.message);
      } else {
        console.log('✅ Supabase connection successful');
        setConnectionStatus('connected');
      }
    } catch (err) {
      console.error('💥 Unexpected error:', err);
      setConnectionStatus('error');
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Supabase Connection Test</Text>
      
      {connectionStatus === 'testing' && (
        <Text style={styles.testing}>Testing connection...</Text>
      )}
      
      {connectionStatus === 'connected' && (
        <Text style={styles.success}>✅ Connected to Supabase</Text>
      )}
      
      {connectionStatus === 'error' && (
        <View>
          <Text style={styles.error}>❌ Connection failed</Text>
          {error && <Text style={styles.errorDetail}>{error}</Text>}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  testing: {
    color: '#666',
  },
  success: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  error: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  errorDetail: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 5,
  },
});
