import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, TextInput, Animated } from "react-native";
import { ChevronDown, ChevronUp, Check, Edit3 } from "lucide-react-native";
import Colors from "@/constants/colors";
import DiagnosisSelector from "./DiagnosisSelector";
import { HairZone, getNaturalToneOptions, getUndertoneOptions } from "@/types/hair-diagnosis";
import { COLOR_TECHNIQUES } from "@/types/desired-photo";
import { 
  DesiredColorAnalysisResult, 
  getContrastText, 
  getDirectionText, 
  getTextureText 
} from "@/types/desired-analysis";
import { 
  MaintenanceLevel, 
  AvoidableTone,
  BudgetLevel,
  getMaintenanceLevelLabel,
  getAvoidableToneLabel,
  getBudgetLevelLabel,
  getAvoidableToneOptions
} from "@/types/lifestyle-preferences";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { 
  getRecommendedTechniques, 
  isTechniqueCompatible, 
  getTechniqueMismatchWarning,
  getTechniqueMaintenanceExplanation 
} from "@/utils/technique-recommendations";

interface DesiredColorAnalysisFormProps {
  analysisResult: DesiredColorAnalysisResult | null;
  onAnalysisChange: (analysis: DesiredColorAnalysisResult) => void;
  isFromAI?: boolean;
}

export default function DesiredColorAnalysisForm({
  analysisResult,
  onAnalysisChange,
  isFromAI = false
}: DesiredColorAnalysisFormProps) {
  const [currentZone, setCurrentZone] = useState<HairZone>(HairZone.ROOTS);
  const [showCustomTechnique, setShowCustomTechnique] = useState(false);
  const [techniqueWarning, setTechniqueWarning] = useState<string | null>(null);
  
  // Get salon configuration
  const { configuration } = useSalonConfigStore();
  
  // Animations
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (analysisResult && isFromAI) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [analysisResult, isFromAI]);

  const updateGeneral = (field: string, value: any) => {
    if (!analysisResult) return;
    
    // Check for technique compatibility when technique changes
    if (field === 'technique' && analysisResult.lifestyle?.maintenanceLevel) {
      const warning = getTechniqueMismatchWarning(value, analysisResult.lifestyle.maintenanceLevel);
      setTechniqueWarning(warning);
    }
    
    onAnalysisChange({
      ...analysisResult,
      general: { ...analysisResult.general, [field]: value },
      isFromAI: false
    });
  };

  const updateZone = (zone: HairZone, field: string, value: any) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      zones: {
        ...analysisResult.zones,
        [zone]: { ...analysisResult.zones[zone], [field]: value }
      },
      isFromAI: false
    });
  };

  const updateAdvanced = (field: string, value: any) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      advanced: { ...analysisResult.advanced, [field]: value },
      isFromAI: false
    });
  };

  const updateLifestyle = (field: string, value: any) => {
    if (!analysisResult) return;
    
    // Update technique warning when maintenance level changes
    if (field === 'maintenanceLevel' && analysisResult.general.technique) {
      const warning = getTechniqueMismatchWarning(analysisResult.general.technique, value);
      setTechniqueWarning(warning);
    }
    
    onAnalysisChange({
      ...analysisResult,
      lifestyle: { ...analysisResult.lifestyle, [field]: value } as any,
      isFromAI: false
    });
  };

  const toggleAvoidTone = (tone: AvoidableTone) => {
    if (!analysisResult) return;
    const currentTones = analysisResult.lifestyle?.avoidTones || [];
    const newTones = currentTones.includes(tone)
      ? currentTones.filter(t => t !== tone)
      : [...currentTones, tone];
    
    updateLifestyle('avoidTones', newTones);
  };

  if (!analysisResult) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {isFromAI 
            ? "Captura o sube fotos del color deseado para comenzar el análisis"
            : "Selecciona el modo Manual para introducir los valores deseados"
          }
        </Text>
      </View>
    );
  }

  // Get recommended techniques based on maintenance level
  const recommendedTechniques = analysisResult?.lifestyle?.maintenanceLevel 
    ? getRecommendedTechniques(analysisResult.lifestyle.maintenanceLevel)
    : [];

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Main Analysis Section */}
      <View style={styles.mainSection}>
        <Text style={styles.mainTitle}>
          Color Deseado
          {isFromAI && <Text style={styles.aiIndicator}> • AI</Text>}
        </Text>

        {/* General fields in two columns */}
        <View style={styles.generalFieldsRow}>
          <View style={styles.halfField}>
            <Text style={styles.label}>
              Nivel deseado
              {analysisResult.isFromAI && <Text style={styles.editIndicator}> •</Text>}
            </Text>
            <TextInput
              style={styles.input}
              value={analysisResult.general.overallLevel}
              onChangeText={(value) => updateGeneral('overallLevel', value)}
              placeholder="8 o 8/9"
            />
          </View>

          <View style={styles.halfField}>
            <Text style={styles.label}>
              Tono principal
              {analysisResult.isFromAI && <Text style={styles.editIndicator}> •</Text>}
            </Text>
            <TextInput
              style={styles.input}
              value={analysisResult.general.overallTone}
              onChangeText={(value) => updateGeneral('overallTone', value)}
              placeholder="Rubio ceniza"
            />
          </View>
        </View>

        {/* Technique Selection */}
        <View style={styles.techniqueSection}>
          <Text style={styles.label}>Técnica de aplicación</Text>
          <View style={styles.techniqueRow}>
            {COLOR_TECHNIQUES.slice(0, 4).map((technique) => (
              <TouchableOpacity
                key={technique.id}
                style={[
                  styles.techniqueButton,
                  analysisResult.general.technique === technique.id && styles.techniqueButtonActive
                ]}
                onPress={() => {
                  updateGeneral('technique', technique.id);
                  setShowCustomTechnique(false);
                }}
              >
                <Text style={styles.techniqueIcon}>{technique.icon}</Text>
                <Text style={[
                  styles.techniqueText,
                  analysisResult.general.technique === technique.id && styles.techniqueTextActive
                ]}>
                  {technique.name.split(' ')[0]}
                </Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[
                styles.techniqueButton,
                showCustomTechnique && styles.techniqueButtonActive
              ]}
              onPress={() => {
                setShowCustomTechnique(true);
                updateGeneral('technique', 'custom');
              }}
            >
              <Text style={styles.techniqueIcon}>➕</Text>
              <Text style={[
                styles.techniqueText,
                showCustomTechnique && styles.techniqueTextActive
              ]}>
                Custom
              </Text>
            </TouchableOpacity>
          </View>
          
          {showCustomTechnique && (
            <TextInput
              style={styles.customTechniqueInput}
              value={analysisResult.general.customTechnique || ''}
              onChangeText={(value) => updateGeneral('customTechnique', value)}
              placeholder="Ej: Balayage invertido con babylights"
              multiline
            />
          )}
        </View>
      </View>

      {/* Zone Analysis Section */}
      <View style={styles.zoneSection}>
        <Text style={styles.sectionTitle}>Análisis por Zonas</Text>
        
        {/* Zone Tabs */}
        <View style={styles.zoneTabContainer}>
          <View style={styles.zoneTabs}>
            {Object.values(HairZone).map((zone) => (
              <TouchableOpacity
                key={zone}
                style={[styles.zoneTab, currentZone === zone && styles.activeZoneTab]}
                onPress={() => setCurrentZone(zone)}
              >
                <Text style={[styles.zoneTabText, currentZone === zone && styles.activeZoneTabText]}>
                  {zone}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Zone Fields in two columns */}
        <View style={styles.zoneFieldsContainer}>
          <View style={styles.zoneFieldsRow}>
            <View style={styles.halfField}>
              <DiagnosisSelector
                label="Nivel"
                value={analysisResult.zones[currentZone]?.desiredLevel?.toString() || ""}
                options={Array.from({length: 10}, (_, i) => i + 1)}
                onValueChange={(value) => updateZone(currentZone, 'desiredLevel', parseInt(value))}
                required
                isFromAI={analysisResult.isFromAI}
              />
            </View>

            <View style={styles.halfField}>
              <DiagnosisSelector
                label="Tono"
                value={analysisResult.zones[currentZone]?.desiredTone || ""}
                options={getNaturalToneOptions()}
                onValueChange={(value) => updateZone(currentZone, 'desiredTone', value)}
                required
                isFromAI={analysisResult.isFromAI}
              />
            </View>
          </View>

          <View style={styles.zoneFieldsRow}>
            <View style={styles.halfField}>
              <DiagnosisSelector
                label="Reflejo"
                value={analysisResult.zones[currentZone]?.desiredUndertone || ""}
                options={getUndertoneOptions()}
                onValueChange={(value) => updateZone(currentZone, 'desiredUndertone', value)}
                required
                isFromAI={analysisResult.isFromAI}
              />
            </View>

            <View style={styles.halfField}>
              <Text style={styles.label}>Cobertura (%)</Text>
              <TextInput
                style={styles.input}
                value={analysisResult.zones[currentZone]?.coverage?.toString() || "100"}
                onChangeText={(value) => updateZone(currentZone, 'coverage', parseInt(value) || 100)}
                keyboardType="numeric"
                placeholder="100"
                maxLength={3}
              />
            </View>
          </View>
        </View>
      </View>

      {/* Lifestyle Preferences - Simplified */}
      <View style={styles.lifestyleSection}>
        <Text style={styles.sectionTitle}>Preferencias</Text>
        
        {/* Maintenance Level */}
        <View style={styles.preferenceGroup}>
          <Text style={styles.label}>Frecuencia de mantenimiento</Text>
          <View style={styles.optionsRow}>
            {[MaintenanceLevel.LOW, MaintenanceLevel.MEDIUM, MaintenanceLevel.HIGH].map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.optionButton,
                  analysisResult.lifestyle?.maintenanceLevel === level && styles.optionButtonActive
                ]}
                onPress={() => updateLifestyle('maintenanceLevel', level)}
              >
                <Text style={[
                  styles.optionButtonText,
                  analysisResult.lifestyle?.maintenanceLevel === level && styles.optionButtonTextActive
                ]}>
                  {getMaintenanceLevelLabel(level)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Contrast and Direction in two columns */}
        <View style={styles.contrastDirectionRow}>
          <View style={styles.halfField}>
            <Text style={styles.label}>Contraste</Text>
            <View style={styles.compactOptionsRow}>
              {(['subtle', 'medium', 'high'] as const).map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.compactOption,
                    analysisResult.advanced.contrast === level && styles.compactOptionActive
                  ]}
                  onPress={() => updateAdvanced('contrast', level)}
                >
                  <Text style={[
                    styles.compactOptionText,
                    analysisResult.advanced.contrast === level && styles.compactOptionTextActive
                  ]}>
                    {level === 'subtle' ? 'Sutil' : level === 'medium' ? 'Medio' : 'Alto'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.halfField}>
            <Text style={styles.label}>Dirección</Text>
            <View style={styles.compactOptionsRow}>
              {(['warmer', 'neutral', 'cooler'] as const).map((dir) => (
                <TouchableOpacity
                  key={dir}
                  style={[
                    styles.compactOption,
                    analysisResult.advanced.direction === dir && styles.compactOptionActive
                  ]}
                  onPress={() => updateAdvanced('direction', dir)}
                >
                  <Text style={[
                    styles.compactOptionText,
                    analysisResult.advanced.direction === dir && styles.compactOptionTextActive
                  ]}>
                    {dir === 'warmer' ? 'Cálido' : dir === 'neutral' ? 'Neutro' : 'Frío'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Tones to Avoid - Compact */}
        <View style={styles.preferenceGroup}>
          <Text style={styles.label}>Evitar tonos</Text>
          <View style={styles.tonesGrid}>
            {getAvoidableToneOptions().slice(0, 4).map((tone) => (
              <TouchableOpacity
                key={tone}
                style={[
                  styles.toneChip,
                  analysisResult.lifestyle?.avoidTones?.includes(tone) && styles.toneChipActive
                ]}
                onPress={() => toggleAvoidTone(tone)}
              >
                <Text style={[
                  styles.toneChipText,
                  analysisResult.lifestyle?.avoidTones?.includes(tone) && styles.toneChipTextActive
                ]}>
                  {getAvoidableToneLabel(tone)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 100,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: "center",
  },
  mainSection: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  mainTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.light.text,
    marginBottom: 20,
  },
  aiIndicator: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: "normal",
  },
  editIndicator: {
    color: Colors.light.primary,
    fontSize: 12,
  },
  generalFieldsRow: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 20,
  },
  halfField: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  techniqueSection: {
    marginBottom: 16,
  },
  techniqueRow: {
    flexDirection: "row",
    gap: 8,
  },
  techniqueButton: {
    flex: 1,
    alignItems: "center",
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  techniqueButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  techniqueIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  techniqueText: {
    fontSize: 11,
    fontWeight: "500",
    color: Colors.light.gray,
  },
  techniqueTextActive: {
    color: "white",
    fontWeight: "600",
  },
  customTechniqueInput: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginTop: 12,
    minHeight: 60,
  },
  contrastDirectionRow: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  compactOptionsRow: {
    flexDirection: "row",
    gap: 4,
  },
  compactOption: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: "center",
  },
  compactOptionActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  compactOptionText: {
    fontSize: 13,
    fontWeight: "600",
    color: Colors.light.text,
  },
  compactOptionTextActive: {
    color: "white",
  },
  zoneSection: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
    marginBottom: 16,
  },
  zoneTabContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 4,
    marginBottom: 20,
  },
  zoneTabs: {
    flexDirection: "row",
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderRadius: 12,
  },
  activeZoneTab: {
    backgroundColor: Colors.light.primary + "20",
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  zoneTabText: {
    fontSize: 15,
    fontWeight: "500",
    color: Colors.light.gray,
  },
  activeZoneTabText: {
    color: Colors.light.primary,
    fontWeight: "700",
  },
  zoneFieldsContainer: {
    gap: 12,
  },
  zoneFieldsRow: {
    flexDirection: "row",
    gap: 12,
  },
  lifestyleSection: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  preferenceGroup: {
    marginBottom: 16,
  },
  optionsRow: {
    flexDirection: "row",
    gap: 8,
  },
  optionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: "center",
  },
  optionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  optionButtonText: {
    fontSize: 13,
    fontWeight: "500",
    color: Colors.light.text,
  },
  optionButtonTextActive: {
    color: "white",
    fontWeight: "600",
  },
  tonesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  toneChip: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  toneChipActive: {
    backgroundColor: Colors.light.error + "10",
    borderColor: Colors.light.error,
  },
  toneChipText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  toneChipTextActive: {
    color: Colors.light.error,
    fontWeight: "600",
  },
});