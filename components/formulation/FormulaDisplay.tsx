import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Platform
} from 'react-native';
import { Edit3, Clock, X, Beaker, ChevronRight, Package, CheckCircle, AlertTriangle, Calendar } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ViabilityAnalysis } from '@/types/formulation';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';

interface Product {
  name: string;
  amount: string;
  purpose?: string;
}

interface Mixture {
  id: string;
  name: string;
  products: Product[];
  zone: string;
  oxidantVolume?: string;
}

interface EnhancedStep {
  number: number;
  title: string;
  description: string[];
  zone?: string;
  mixtureId?: string;
  timing?: string;
  warning?: string;
}

interface ParsedFormula {
  brand: string;
  line: string;
  technique: string;
  allProducts: Product[];
  mixtures: Mixture[];
  processingTime: string;
  steps: EnhancedStep[];
  sessionInfo?: {
    current: number;
    total: number;
    maxLevel?: number;
  };
}

interface FormulaDisplayProps {
  formulaText: string;
  clientName?: string;
  serviceDate?: string;
  onEdit?: (newFormula: string) => void;
  editable?: boolean;
  viabilityAnalysis?: ViabilityAnalysis;
  currentLevel?: number;
  targetLevel?: number;
}

const parseFormulaText = (text: string, viability?: ViabilityAnalysis, currentLevel?: number, targetLevel?: number, developerTerm?: string): ParsedFormula => {
  const lines = text.split('\n').filter(line => line.trim());
  
  let brand = '';
  let line = '';
  let technique = '';
  const allProducts: Product[] = [];
  let processingTime = '';
  const rawSteps: string[] = [];
  
  let inSteps = false;
  
  // Extract basic info from formula text
  lines.forEach((textLine) => {
    const trimmed = textLine.trim();
    
    if (trimmed.includes('Professionals') || trimmed.includes('Professional')) {
      const parts = trimmed.split(' ');
      brand = parts[0];
      line = parts.slice(1).join(' ');
    }
    
    if (trimmed.toLowerCase().includes('fórmula para')) {
      technique = trimmed.split('para')[1]?.replace(':', '').trim() || '';
    }
    
    if (trimmed.startsWith('-')) {
      const match = trimmed.match(/- (.+?) \((.+?)\)(.*)/);
      if (match) {
        allProducts.push({
          name: match[1].trim(),
          amount: match[2].trim(),
          purpose: match[3] ? match[3].replace(/para|:/g, '').trim() : undefined
        });
      }
    }
    
    if (trimmed.toLowerCase().includes('tiempo de proceso')) {
      const match = trimmed.match(/(\d+)\s*(minutos|min)/);
      if (match) {
        processingTime = `${match[1]} minutos`;
      }
    }
    
    if (trimmed.toLowerCase().includes('pasos de aplicación')) {
      inSteps = true;
    }
    
    if (inSteps && /^\d+\./.test(trimmed)) {
      rawSteps.push(trimmed);
    }
  });
  
  // Create mixtures based on oxidant purposes
  const mixtures: Mixture[] = [];
  const searchTerm = developerTerm || 'oxidante';
  const colorProducts = allProducts.filter(p => !p.name.toLowerCase().includes(searchTerm.toLowerCase()));
  const oxidants = allProducts.filter(p => p.name.toLowerCase().includes(searchTerm.toLowerCase()));
  
  // Mixture A - for mids/ends (usually higher volume oxidant)
  const highVolOxidant = oxidants.find(o => o.name.includes('30') || o.purpose?.includes('medios'));
  if (highVolOxidant) {
    mixtures.push({
      id: 'A',
      name: 'Medios/Puntas',
      products: [...colorProducts, highVolOxidant],
      zone: 'medios y puntas',
      oxidantVolume: '30'
    });
  }
  
  // Mixture B - for roots (usually lower volume oxidant)
  const lowVolOxidant = oxidants.find(o => o.name.includes('20') || o.purpose?.includes('raíces'));
  if (lowVolOxidant) {
    mixtures.push({
      id: 'B',
      name: 'Raíces',
      products: [...colorProducts, lowVolOxidant],
      zone: 'raíces',
      oxidantVolume: '20'
    });
  }
  
  // If no specific oxidants found, create single mixture
  if (mixtures.length === 0 && oxidants.length > 0) {
    mixtures.push({
      id: 'A',
      name: 'Mezcla Principal',
      products: allProducts,
      zone: 'todo el cabello',
      oxidantVolume: oxidants[0].name.match(/\d+/)?.[0]
    });
  }
  
  // Parse steps into enhanced format
  const steps: EnhancedStep[] = rawSteps.map((step, index) => {
    const match = step.match(/^(\d+)\.\s*(.+)/);
    const stepNumber = match ? parseInt(match[1]) : index + 1;
    const stepText = match ? match[2] : step;
    
    // Detect zones and mixtures in step text
    const lowerStep = stepText.toLowerCase();
    let zone = '';
    let mixtureId = '';
    let timing = '';
    
    if (lowerStep.includes('raíz') || lowerStep.includes('raíces')) {
      zone = 'Raíces';
      mixtureId = 'B';
    } else if (lowerStep.includes('medios') || lowerStep.includes('puntas')) {
      zone = 'Medios y Puntas';
      mixtureId = 'A';
    }
    
    // Extract timing if mentioned
    const timeMatch = lowerStep.match(/(\d+)\s*min/);
    if (timeMatch) {
      timing = `${timeMatch[1]} min`;
    }
    
    // Create structured step
    return {
      number: stepNumber,
      title: getStepTitle(stepText),
      description: [stepText],
      zone,
      mixtureId,
      timing,
      warning: getStepWarning(stepNumber, viability, currentLevel, targetLevel)
    };
  });
  
  // Add session info from viability if available
  let sessionInfo;
  if (viability && viability.factors.estimatedSessions > 1 && currentLevel && targetLevel) {
    sessionInfo = {
      current: 1,
      total: viability.factors.estimatedSessions,
      maxLevel: Math.min(currentLevel + 2, targetLevel)
    };
  }
  
  return {
    brand: brand || 'Marca',
    line: line || 'Línea Profesional',
    technique: technique || 'Técnica',
    allProducts,
    mixtures,
    processingTime: processingTime || '30 minutos',
    steps,
    sessionInfo
  };
};

// Helper functions
const getStepTitle = (stepText: string): string => {
  const lower = stepText.toLowerCase();
  if (lower.includes('seccionar') || lower.includes('dividir')) return 'División Profesional';
  if (lower.includes('aplicar') && lower.includes('medios')) return 'Aplicación Medios/Puntas';
  if (lower.includes('aplicar') && lower.includes('raíz')) return 'Aplicación Raíces';
  if (lower.includes('envolver') || lower.includes('aluminio')) return 'Técnica de Aluminio';
  if (lower.includes('control') || lower.includes('verificar')) return 'Control Final';
  return 'Proceso';
};

const getStepWarning = (stepNumber: number, viability?: ViabilityAnalysis, currentLevel?: number, targetLevel?: number): string | undefined => {
  if (!viability || viability.factors.estimatedSessions <= 1) return undefined;
  
  const maxLevel = currentLevel && targetLevel ? Math.min(currentLevel + 2, targetLevel) : undefined;
  
  if (stepNumber === 2 && maxLevel) {
    return `SESIÓN 1: No exceder nivel ${maxLevel}`;
  }
  
  if (stepNumber === 4 && maxLevel) {
    return `Verificar nivel ${maxLevel} alcanzado`;
  }
  
  return undefined;
};

export default function FormulaDisplay({
  formulaText,
  onEdit,
  editable = true,
  viabilityAnalysis,
  currentLevel,
  targetLevel
}: FormulaDisplayProps) {
  const { getTerminology, formatVolume, formatWeight, volumeUnit, weightUnit } = useRegionalUnits();
  const [showEditModal, setShowEditModal] = useState(false);
  const [editedFormula, setEditedFormula] = useState(formulaText);
  
  const formula = parseFormulaText(formulaText, viabilityAnalysis, currentLevel, targetLevel, getTerminology('developer'));
  
  const handleSave = () => {
    if (onEdit) {
      onEdit(editedFormula);
    }
    setShowEditModal(false);
  };
  
  const handleCancel = () => {
    setEditedFormula(formulaText);
    setShowEditModal(false);
  };
  
  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Viability Summary Card */}
        {viabilityAnalysis && (
          <View style={[styles.viabilityCard, 
            viabilityAnalysis.score === 'safe' ? styles.viableCard : styles.cautionCard
          ]}>
            <View style={styles.viabilityHeader}>
              {viabilityAnalysis.score === 'safe' ? (
                <CheckCircle size={20} color={Colors.light.success} />
              ) : (
                <AlertTriangle size={20} color={Colors.light.warning} />
              )}
              <Text style={[
                styles.viabilityTitle,
                viabilityAnalysis.score === 'safe' ? styles.viableText : styles.cautionText
              ]}>
                {viabilityAnalysis.factors.estimatedSessions > 1 
                  ? `Proceso en ${viabilityAnalysis.factors.estimatedSessions} Sesiones`
                  : 'Proceso Seguro - 1 Sesión'
                }
              </Text>
            </View>
            
            {viabilityAnalysis.factors.estimatedSessions > 1 && formula.sessionInfo && currentLevel && targetLevel && (
              <View style={styles.sessionInfo}>
                <Text style={styles.sessionText}>
                  <Text style={styles.boldText}>HOY</Text>: Nivel {currentLevel} → <Text style={styles.boldText}>{formula.sessionInfo.maxLevel}</Text> (máximo seguro)
                </Text>
                <Text style={styles.sessionText}>
                  <Text style={styles.boldText}>PRÓXIMA</Text>: Nivel {formula.sessionInfo.maxLevel} → <Text style={styles.boldText}>{targetLevel}</Text> (en 3 semanas)
                </Text>
              </View>
            )}
            
            {viabilityAnalysis.factors.estimatedSessions <= 1 && currentLevel && targetLevel && (
              <Text style={styles.viabilityDetail}>
                • De nivel {currentLevel} → nivel <Text style={styles.boldText}>{targetLevel}</Text> (<Text style={styles.boldText}>{viabilityAnalysis.factors.levelDifference} niveles</Text>)
                {viabilityAnalysis.score === 'safe' && '\n• Alcanzable en una aplicación\n• Sin riesgos detectados'}
              </Text>
            )}
          </View>
        )}
        
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.brandInfo}>
              <Text style={styles.brandText}>{formula.brand}</Text>
              <Text style={styles.lineText}>{formula.line}</Text>
            </View>
            <Text style={styles.techniqueText}>{formula.technique}</Text>
          </View>
          {editable && (
            <TouchableOpacity onPress={() => setShowEditModal(true)} style={styles.editButton}>
              <Edit3 size={18} color={Colors.light.primary} />
            </TouchableOpacity>
          )}
        </View>
        
        {/* Material Needed Card */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Package size={20} color={Colors.light.primary} />
            <Text style={styles.sectionTitle}>Productos para esta fórmula</Text>
          </View>
          
          {formula.sessionInfo && (
            <Text style={styles.sessionLabel}>
              <Text style={styles.boldText}>SESIÓN {formula.sessionInfo.current} de {formula.sessionInfo.total}</Text> - Proceso parcial
            </Text>
          )}
          
          {formula.allProducts.map((product, index) => (
            <View key={index} style={styles.materialItem}>
              <Text style={styles.materialText}>• {product.name} - <Text style={styles.boldText}>{product.amount}</Text></Text>
              {product.purpose && (
                <Text style={styles.materialPurpose}>  ({product.purpose})</Text>
              )}
            </View>
          ))}
          
          <View style={styles.verifySection}>
            <CheckCircle size={16} color={Colors.light.success} />
            <Text style={styles.verifyText}>Verificar test de alergia (48h)</Text>
          </View>
        </View>
        
        {/* Mixtures Preparation Card */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Beaker size={20} color={Colors.light.primary} />
            <Text style={styles.sectionTitle}>Preparación Profesional</Text>
          </View>
          
          {formula.mixtures.map((mixture, index) => (
            <View key={mixture.id} style={styles.mixtureContainer}>
              <Text style={styles.mixtureName}>MEZCLA {mixture.id} - {mixture.name}</Text>
              
              <View style={styles.mixtureSteps}>
                <Text style={styles.mixtureStep}>1. Bowl en balanza → poner a cero</Text>
                {mixture.products.filter(p => !p.name.toLowerCase().includes(getTerminology('developer').toLowerCase())).map((product, idx) => (
                  <Text key={idx} style={styles.mixtureStep}>
                    {idx + 2}. Añadir: <Text style={styles.boldText}>{product.amount}</Text> de {product.name}
                  </Text>
                ))}
                <Text style={styles.mixtureStep}>
                  {mixture.products.filter(p => !p.name.toLowerCase().includes(getTerminology('developer').toLowerCase())).length + 2}. Poner a cero → añadir <Text style={styles.boldText}>{mixture.products.find(p => p.name.toLowerCase().includes(getTerminology('developer').toLowerCase()))?.amount || '40ml'}</Text> {getTerminology('developer')} {mixture.oxidantVolume}v
                </Text>
                <Text style={styles.mixtureStep}>
                  {mixture.products.filter(p => !p.name.toLowerCase().includes(getTerminology('developer').toLowerCase())).length + 3}. Mezclar hasta cremosidad uniforme
                </Text>
              </View>
              
              {mixture.id === 'B' && (
                <Text style={styles.mixtureNote}>
                  • Preparar cuando medios estén procesando
                </Text>
              )}
            </View>
          ))}
        </View>
        
        {/* Application Steps */}
        {formula.steps.map((step, index) => (
          <View key={index} style={styles.stepSection}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>{step.number}</Text>
              </View>
              <Text style={styles.stepTitle}>{step.title}</Text>
            </View>
            
            {step.zone && (
              <Text style={styles.stepZone}>Zona: <Text style={styles.boldText}>{step.zone}</Text></Text>
            )}
            
            {step.mixtureId && (
              <Text style={styles.stepMixture}>Usar: MEZCLA {step.mixtureId}</Text>
            )}
            
            <View style={styles.stepDetails}>
              {step.description.map((desc, idx) => {
                // Parse description into bullet points
                const points = desc.split('.').filter(p => p.trim());
                if (points.length > 1) {
                  return points.map((point, pidx) => (
                    <Text key={`${idx}-${pidx}`} style={styles.stepPoint}>
                      • {point.trim()}
                    </Text>
                  ));
                } else {
                  return <Text key={idx} style={styles.stepDescription}>{desc}</Text>;
                }
              })}
            </View>
            
            {step.timing && (
              <View style={styles.stepTiming}>
                <Clock size={14} color={Colors.light.primary} />
                <Text style={styles.stepTimingText}>{step.timing}</Text>
              </View>
            )}
            
            {step.warning && (
              <View style={styles.stepWarning}>
                <AlertTriangle size={14} color={Colors.light.warning} />
                <Text style={styles.stepWarningText}>{step.warning}</Text>
              </View>
            )}
          </View>
        ))}
        
        {/* Time Guide Card */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color={Colors.light.primary} />
            <Text style={styles.sectionTitle}>Secuencia Temporal</Text>
          </View>
          
          <View style={styles.timelineContainer}>
            <Text style={styles.timelineItem}>INICIO → Aplicar medios/puntas</Text>
            <Text style={styles.timelineItem}>+20min → Preparar y aplicar raíces</Text>
            <Text style={styles.timelineItem}>+30min → Verificar resultado</Text>
            <Text style={styles.timelineItem}>+35min → Enjuagar</Text>
          </View>
          
          <Text style={styles.timelineNote}>📝 Anota hora de inicio</Text>
        </View>
        
        {/* Next Session Card (if multi-session) */}
        {formula.sessionInfo && formula.sessionInfo.total > 1 && (
          <View style={[styles.section, styles.nextSessionCard]}>
            <View style={styles.sectionHeader}>
              <Calendar size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Siguiente Sesión</Text>
            </View>
            
            <Text style={styles.nextSessionText}>• Programar en 3-4 semanas</Text>
            <Text style={styles.nextSessionText}>
              • Objetivo: Completar hasta nivel {targetLevel}
            </Text>
            <Text style={styles.nextSessionText}>• Tratamiento reparador semanal</Text>
          </View>
        )}
      </ScrollView>
      
      {/* Edit Modal */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        transparent={true}
        onRequestClose={handleCancel}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={handleCancel}>
                <X size={24} color={Colors.light.gray} />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>Editar Fórmula</Text>
              <TouchableOpacity onPress={handleSave}>
                <Text style={styles.saveButton}>Guardar</Text>
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={styles.formulaInput}
              value={editedFormula}
              onChangeText={setEditedFormula}
              multiline
              textAlignVertical="top"
              placeholder="Ingresa la fórmula..."
            />
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  viabilityCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  viableCard: {
    backgroundColor: Colors.light.success + '10',
    borderWidth: 1,
    borderColor: Colors.light.success + '30',
  },
  cautionCard: {
    backgroundColor: Colors.light.warning + '10',
    borderWidth: 1,
    borderColor: Colors.light.warning + '30',
  },
  viabilityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  viabilityTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  viableText: {
    color: Colors.light.success,
  },
  cautionText: {
    color: Colors.light.warning,
  },
  viabilityDetail: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  sessionInfo: {
    marginTop: 8,
  },
  sessionText: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
  },
  header: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerContent: {
    flex: 1,
  },
  brandInfo: {
    marginBottom: 8,
  },
  brandText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
  },
  lineText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  techniqueText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  editButton: {
    padding: 8,
    marginLeft: 8,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  sessionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 12,
  },
  materialItem: {
    marginBottom: 8,
  },
  materialText: {
    fontSize: 15,
    color: Colors.light.text,
  },
  materialPurpose: {
    fontSize: 13,
    color: Colors.light.gray,
    marginLeft: 16,
  },
  verifySection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  verifyText: {
    fontSize: 14,
    color: Colors.light.success,
  },
  mixtureContainer: {
    marginBottom: 20,
  },
  mixtureName: {
    fontSize: 15,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 8,
  },
  mixtureSteps: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 12,
  },
  mixtureStep: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
    lineHeight: 20,
  },
  mixtureNote: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 8,
    fontStyle: 'italic',
  },
  stepSection: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepNumber: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 16,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    flex: 1,
  },
  stepZone: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 8,
  },
  stepMixture: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 12,
    backgroundColor: Colors.light.primary + '15',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 10,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: Colors.light.primary + '30',
  },
  stepDetails: {
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 15,
    color: Colors.light.text,
    lineHeight: 22,
    marginBottom: 4,
  },
  stepPoint: {
    fontSize: 15,
    color: Colors.light.text,
    lineHeight: 22,
    marginBottom: 4,
    marginLeft: 8,
  },
  stepTiming: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 8,
  },
  stepTimingText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  stepWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 8,
    backgroundColor: Colors.light.warning + '10',
    padding: 8,
    borderRadius: 8,
  },
  stepWarningText: {
    fontSize: 13,
    color: Colors.light.warning,
    fontWeight: '600',
    flex: 1,
  },
  timelineContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 16,
  },
  timelineItem: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 8,
    lineHeight: 20,
  },
  timelineNote: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 8,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  nextSessionCard: {
    backgroundColor: Colors.light.primary + '05',
    borderWidth: 1,
    borderColor: Colors.light.primary + '20',
  },
  nextSessionText: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 6,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '80%',
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  formulaInput: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    textAlignVertical: 'top',
  },
  boldText: {
    fontWeight: '700',
  },
});