import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { LegacyDataCleaner } from '@/utils/legacyDataCleaner';
import { useAuthStore } from '@/stores/auth-store';

export default function LegacyDataCleanerComponent() {
  const [isClearing, setIsClearing] = useState(false);
  const [lastCleanup, setLastCleanup] = useState<string | null>(null);
  const { clearLegacyBrandPreferences } = useAuthStore();

  const handleQuickClean = async () => {
    try {
      setIsClearing(true);
      console.log('🧹 Starting quick legacy data cleanup...');
      
      // Clear from auth store
      await clearLegacyBrandPreferences();
      
      setLastCleanup(new Date().toLocaleTimeString());
      
      Alert.alert(
        'Limpieza Completada',
        'Los datos legacy han sido limpiados del store de autenticación.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error in quick cleanup:', error);
      Alert.alert(
        'Error',
        'Hubo un error durante la limpieza rápida.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsClearing(false);
    }
  };

  const handleFullClean = async () => {
    Alert.alert(
      'Limpieza Completa',
      'Esto limpiará TODOS los datos legacy de AsyncStorage. ¿Estás seguro?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpiar Todo',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsClearing(true);
              console.log('🧹 Starting full legacy data cleanup...');
              
              await LegacyDataCleaner.cleanAllLegacyData();
              
              setLastCleanup(new Date().toLocaleTimeString());
              
              Alert.alert(
                'Limpieza Completada',
                'Todos los datos legacy han sido limpiados. Reinicia la aplicación para ver los cambios.',
                [{ text: 'OK' }]
              );
            } catch (error) {
              console.error('Error in full cleanup:', error);
              Alert.alert(
                'Error',
                'Hubo un error durante la limpieza completa.',
                [{ text: 'OK' }]
              );
            } finally {
              setIsClearing(false);
            }
          }
        }
      ]
    );
  };

  const handleNuclearOption = async () => {
    Alert.alert(
      '⚠️ OPCIÓN NUCLEAR ⚠️',
      'Esto ELIMINARÁ TODOS los datos de AsyncStorage. Perderás TODA la configuración guardada. ¿Estás ABSOLUTAMENTE seguro?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'ELIMINAR TODO',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsClearing(true);
              await LegacyDataCleaner.clearAllAsyncStorage();
              
              Alert.alert(
                'Datos Eliminados',
                'TODOS los datos han sido eliminados. Reinicia la aplicación.',
                [{ text: 'OK' }]
              );
            } catch (error) {
              console.error('Error in nuclear cleanup:', error);
              Alert.alert('Error', 'Error eliminando datos.', [{ text: 'OK' }]);
            } finally {
              setIsClearing(false);
            }
          }
        }
      ]
    );
  };

  const handleDebugStorage = async () => {
    try {
      await LegacyDataCleaner.debugAsyncStorage();
      Alert.alert(
        'Debug Completado',
        'Revisa la consola para ver el contenido de AsyncStorage.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error debugging storage:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧹 Limpiador de Datos Legacy</Text>
      <Text style={styles.subtitle}>
        Herramientas para limpiar datos antiguos que causan errores de UUID
      </Text>
      
      {lastCleanup && (
        <Text style={styles.lastCleanup}>
          Última limpieza: {lastCleanup}
        </Text>
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.quickButton]}
          onPress={handleQuickClean}
          disabled={isClearing}
        >
          {isClearing ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Text style={styles.buttonText}>🚀 Limpieza Rápida</Text>
              <Text style={styles.buttonSubtext}>Solo auth store</Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.fullButton]}
          onPress={handleFullClean}
          disabled={isClearing}
        >
          <Text style={styles.buttonText}>🧹 Limpieza Completa</Text>
          <Text style={styles.buttonSubtext}>Todos los datos legacy</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.debugButton]}
          onPress={handleDebugStorage}
          disabled={isClearing}
        >
          <Text style={styles.buttonText}>🔍 Debug Storage</Text>
          <Text style={styles.buttonSubtext}>Ver contenido</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.nuclearButton]}
          onPress={handleNuclearOption}
          disabled={isClearing}
        >
          <Text style={styles.buttonText}>💥 OPCIÓN NUCLEAR</Text>
          <Text style={styles.buttonSubtext}>Eliminar TODO</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.warningContainer}>
        <Text style={styles.warningText}>
          ⚠️ Estas herramientas están diseñadas para solucionar errores de UUID causados por datos legacy.
        </Text>
        <Text style={styles.warningText}>
          💡 Prueba primero la "Limpieza Rápida". Si el error persiste, usa "Limpieza Completa".
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    margin: 10,
    borderWidth: 2,
    borderColor: '#e74c3c',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    color: '#7f8c8d',
    marginBottom: 16,
  },
  lastCleanup: {
    fontSize: 12,
    textAlign: 'center',
    color: '#27ae60',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  quickButton: {
    backgroundColor: '#3498db',
  },
  fullButton: {
    backgroundColor: '#f39c12',
  },
  debugButton: {
    backgroundColor: '#9b59b6',
  },
  nuclearButton: {
    backgroundColor: '#e74c3c',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonSubtext: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
    marginTop: 2,
  },
  warningContainer: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#fff3cd',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  warningText: {
    fontSize: 12,
    color: '#856404',
    marginBottom: 4,
  },
});
