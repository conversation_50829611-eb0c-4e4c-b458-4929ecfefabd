#!/usr/bin/env node

/**
 * Test script for Supabase integration
 * This script runs basic connectivity and functionality tests
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please ensure EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔗 Testing Supabase connection...');
  
  try {
    const { data, error } = await supabase
      .from('brands')
      .select('count')
      .limit(1);

    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    console.log('✅ Connection successful');
    return true;
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
}

async function testTables() {
  console.log('\n📋 Testing table access...');
  
  const tables = [
    'brands',
    'product_lines', 
    'products',
    'salons',
    'profiles',
    'clients',
    'salon_inventory',
    'hair_analyses',
    'formulations',
    'formulation_products',
    'salon_brand_preferences',
    'ai_service_configs',
    'audit_logs'
  ];

  const results = {};

  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1);

      if (error) {
        results[table] = `❌ ${error.message}`;
      } else {
        results[table] = '✅ Accessible';
      }
    } catch (error) {
      results[table] = `❌ ${error.message}`;
    }
  }

  console.log('\nTable Access Results:');
  Object.entries(results).forEach(([table, status]) => {
    console.log(`  ${table}: ${status}`);
  });

  return results;
}

async function testFunctions() {
  console.log('\n⚙️ Testing database functions...');
  
  const functions = [
    {
      name: 'get_user_salon_id',
      params: { user_id: '00000000-0000-0000-0000-000000000000' }
    },
    {
      name: 'user_has_permission',
      params: { 
        user_id: '00000000-0000-0000-0000-000000000000',
        permission_name: 'manage_clients'
      }
    },
    {
      name: 'validate_operation_permission',
      params: { operation_type: 'manage_clients' }
    }
  ];

  const results = {};

  for (const func of functions) {
    try {
      const { data, error } = await supabase.rpc(func.name, func.params);

      if (error) {
        results[func.name] = `❌ ${error.message}`;
      } else {
        results[func.name] = '✅ Working';
      }
    } catch (error) {
      results[func.name] = `❌ ${error.message}`;
    }
  }

  console.log('\nFunction Test Results:');
  Object.entries(results).forEach(([func, status]) => {
    console.log(`  ${func}: ${status}`);
  });

  return results;
}

async function testAuth() {
  console.log('\n🔐 Testing authentication...');
  
  try {
    // Test getting current session (should be null)
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Auth error:', error.message);
      return false;
    }

    if (session) {
      console.log('ℹ️ Already authenticated as:', session.user.email);
    } else {
      console.log('✅ Auth system accessible (no active session)');
    }

    return true;
  } catch (error) {
    console.log('❌ Auth test failed:', error.message);
    return false;
  }
}

async function testRLS() {
  console.log('\n🛡️ Testing Row Level Security...');
  
  try {
    // Ensure we're not authenticated
    await supabase.auth.signOut();
    
    // Try to access protected data
    const { data, error } = await supabase
      .from('salons')
      .select('*');

    if (data && data.length === 0) {
      console.log('✅ RLS working - no unauthorized access to salons');
      return true;
    } else if (error && error.message.includes('permission')) {
      console.log('✅ RLS working - permission denied');
      return true;
    } else {
      console.log('⚠️ RLS may not be working properly');
      return false;
    }
  } catch (error) {
    console.log('❌ RLS test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🧪 Starting Supabase Integration Tests\n');
  console.log('='.repeat(50));

  const results = {
    connection: await testConnection(),
    tables: await testTables(),
    functions: await testFunctions(),
    auth: await testAuth(),
    rls: await testRLS()
  };

  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Summary:');
  console.log(`Connection: ${results.connection ? '✅' : '❌'}`);
  console.log(`Authentication: ${results.auth ? '✅' : '❌'}`);
  console.log(`RLS: ${results.rls ? '✅' : '❌'}`);

  const tableSuccesses = Object.values(results.tables).filter(r => r.includes('✅')).length;
  const tableFails = Object.values(results.tables).filter(r => r.includes('❌')).length;
  console.log(`Tables: ${tableSuccesses} ✅, ${tableFails} ❌`);

  const functionSuccesses = Object.values(results.functions).filter(r => r.includes('✅')).length;
  const functionFails = Object.values(results.functions).filter(r => r.includes('❌')).length;
  console.log(`Functions: ${functionSuccesses} ✅, ${functionFails} ❌`);

  const overallSuccess = results.connection && results.auth && results.rls && 
                         tableFails === 0 && functionFails === 0;

  console.log('\n' + '='.repeat(50));
  if (overallSuccess) {
    console.log('🎉 All tests passed! Supabase integration is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the configuration and database setup.');
  }

  return overallSuccess;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
