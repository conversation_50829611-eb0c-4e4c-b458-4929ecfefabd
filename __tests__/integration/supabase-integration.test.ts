import { supabase } from '@/config/supabase';

// Integration tests for Supabase connection and basic functionality
// These tests require a working Supabase connection
describe('Supabase Integration', () => {
  // Skip these tests in CI/CD unless SUPABASE_TEST_MODE is set
  const shouldRunIntegrationTests = process.env.SUPABASE_TEST_MODE === 'true';

  beforeAll(() => {
    if (!shouldRunIntegrationTests) {
      console.log('Skipping Supabase integration tests. Set SUPABASE_TEST_MODE=true to run.');
    }
  });

  describe('Database Connection', () => {
    it('should connect to Supabase successfully', async () => {
      if (!shouldRunIntegrationTests) return;

      // Test basic connection by querying a simple table
      const { data, error } = await supabase
        .from('brands')
        .select('count')
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });
  });

  describe('Authentication', () => {
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    let testUserId: string;

    afterAll(async () => {
      if (!shouldRunIntegrationTests || !testUserId) return;

      // Clean up test user
      try {
        await supabase.auth.admin.deleteUser(testUserId);
      } catch (error) {
        console.warn('Failed to clean up test user:', error);
      }
    });

    it('should sign up a new user', async () => {
      if (!shouldRunIntegrationTests) return;

      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          data: {
            name: 'Test User',
          },
        },
      });

      expect(error).toBeNull();
      expect(data.user).toBeDefined();
      expect(data.user?.email).toBe(testEmail);

      if (data.user) {
        testUserId = data.user.id;
      }
    });

    it('should create a profile automatically', async () => {
      if (!shouldRunIntegrationTests || !testUserId) return;

      // Wait a bit for the trigger to execute
      await new Promise(resolve => setTimeout(resolve, 1000));

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', testUserId)
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.email).toBe(testEmail);
      expect(data?.name).toBe('Test User');
    });

    it('should sign in with correct credentials', async () => {
      if (!shouldRunIntegrationTests) return;

      const { data, error } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword,
      });

      expect(error).toBeNull();
      expect(data.user).toBeDefined();
      expect(data.user?.email).toBe(testEmail);
    });

    it('should reject invalid credentials', async () => {
      if (!shouldRunIntegrationTests) return;

      const { data, error } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: 'wrongpassword',
      });

      expect(error).toBeDefined();
      expect(data.user).toBeNull();
    });
  });

  describe('Row Level Security', () => {
    it('should prevent unauthorized access to salon data', async () => {
      if (!shouldRunIntegrationTests) return;

      // Try to access salons without authentication
      await supabase.auth.signOut();

      const { data, error } = await supabase
        .from('salons')
        .select('*');

      // Should either return empty data or an error due to RLS
      expect(data).toEqual([]);
    });

    it('should allow access to public data', async () => {
      if (!shouldRunIntegrationTests) return;

      // Brands should be publicly readable
      const { data, error } = await supabase
        .from('brands')
        .select('*')
        .eq('is_active', true)
        .limit(5);

      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
    });
  });

  describe('Database Functions', () => {
    it('should execute get_user_salon_id function', async () => {
      if (!shouldRunIntegrationTests) return;

      // This should return null for unauthenticated users
      const { data, error } = await supabase.rpc('get_user_salon_id', {
        user_id: '00000000-0000-0000-0000-000000000000'
      });

      expect(error).toBeNull();
      expect(data).toBeNull();
    });

    it('should execute user_has_permission function', async () => {
      if (!shouldRunIntegrationTests) return;

      const { data, error } = await supabase.rpc('user_has_permission', {
        user_id: '00000000-0000-0000-0000-000000000000',
        permission_name: 'manage_clients'
      });

      expect(error).toBeNull();
      expect(data).toBe(false);
    });

    it('should execute validate_operation_permission function', async () => {
      if (!shouldRunIntegrationTests) return;

      const { data, error } = await supabase.rpc('validate_operation_permission', {
        operation_type: 'manage_clients'
      });

      expect(error).toBeNull();
      expect(data).toBe(false); // Should be false for unauthenticated users
    });
  });

  describe('Data Integrity', () => {
    it('should enforce foreign key constraints', async () => {
      if (!shouldRunIntegrationTests) return;

      // Try to insert a client with invalid salon_id
      const { data, error } = await supabase
        .from('clients')
        .insert({
          salon_id: '00000000-0000-0000-0000-000000000000',
          name: 'Test Client',
        });

      // Should fail due to foreign key constraint or RLS
      expect(error).toBeDefined();
    });

    it('should enforce unique constraints', async () => {
      if (!shouldRunIntegrationTests) return;

      // Try to insert duplicate brand name
      const brandName = `Test Brand ${Date.now()}`;

      // First insertion should succeed
      const { data: firstInsert, error: firstError } = await supabase
        .from('brands')
        .insert({
          name: brandName,
          is_active: true,
        });

      // Second insertion with same name should fail
      const { data: secondInsert, error: secondError } = await supabase
        .from('brands')
        .insert({
          name: brandName,
          is_active: true,
        });

      expect(secondError).toBeDefined();
      expect(secondError?.message).toContain('duplicate');
    });
  });

  describe('Triggers and Automation', () => {
    it('should automatically update updated_at timestamps', async () => {
      if (!shouldRunIntegrationTests) return;

      // Create a brand
      const brandName = `Auto Update Test ${Date.now()}`;
      const { data: brand, error: createError } = await supabase
        .from('brands')
        .insert({
          name: brandName,
          is_active: true,
        })
        .select()
        .single();

      expect(createError).toBeNull();
      expect(brand).toBeDefined();

      const originalUpdatedAt = brand?.updated_at;

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the brand
      const { data: updatedBrand, error: updateError } = await supabase
        .from('brands')
        .update({
          description: 'Updated description',
        })
        .eq('id', brand?.id)
        .select()
        .single();

      expect(updateError).toBeNull();
      expect(updatedBrand?.updated_at).not.toBe(originalUpdatedAt);
      expect(new Date(updatedBrand?.updated_at).getTime()).toBeGreaterThan(
        new Date(originalUpdatedAt).getTime()
      );

      // Clean up
      await supabase
        .from('brands')
        .delete()
        .eq('id', brand?.id);
    });
  });

  describe('Performance', () => {
    it('should execute queries within reasonable time', async () => {
      if (!shouldRunIntegrationTests) return;

      const startTime = Date.now();

      const { data, error } = await supabase
        .from('brands')
        .select('*')
        .eq('is_active', true)
        .limit(10);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      expect(error).toBeNull();
      expect(queryTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests', async () => {
      if (!shouldRunIntegrationTests) return;

      const promises = Array.from({ length: 5 }, () =>
        supabase
          .from('brands')
          .select('count')
          .limit(1)
      );

      const results = await Promise.all(promises);

      results.forEach(({ error }) => {
        expect(error).toBeNull();
      });
    });
  });
});
