import { authService } from '@/services/authService';
import { supabase } from '@/config/supabase';

// Mock Supabase
jest.mock('@/config/supabase', () => ({
  supabase: {
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      resetPasswordForEmail: jest.fn(),
      updateUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(),
          })),
        })),
      })),
    })),
  },
}));

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signUp', () => {
    it('should successfully sign up a new user', async () => {
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
      };

      const mockProfile = {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        is_owner: false,
        salon_id: null,
        permissions: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      (supabase.auth.signUp as jest.Mock).mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockProfile,
              error: null,
            }),
          }),
        }),
      });

      const result = await authService.signUp('<EMAIL>', 'password123', 'Test User');

      expect(result.user).toEqual(mockUser);
      expect(result.profile).toEqual(mockProfile);
      expect(result.error).toBeNull();
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            name: 'Test User',
          },
        },
      });
    });

    it('should handle sign up errors', async () => {
      const mockError = { message: 'Email already exists' };

      (supabase.auth.signUp as jest.Mock).mockResolvedValue({
        data: { user: null },
        error: mockError,
      });

      const result = await authService.signUp('<EMAIL>', 'password123', 'Test User');

      expect(result.user).toBeNull();
      expect(result.profile).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('signIn', () => {
    it('should successfully sign in a user', async () => {
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
      };

      const mockProfile = {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        is_owner: true,
        salon_id: 'test-salon-id',
        permissions: ['manage_clients'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const mockSalon = {
        id: 'test-salon-id',
        name: 'Test Salon',
        address: '123 Test St',
        phone: '555-0123',
        email: '<EMAIL>',
        owner_id: 'test-user-id',
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // Mock profile fetch
      const profileQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockProfile,
              error: null,
            }),
          }),
        }),
      };

      // Mock salon fetch
      const salonQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockSalon,
              error: null,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(profileQuery)
        .mockReturnValueOnce(salonQuery);

      const result = await authService.signIn('<EMAIL>', 'password123');

      expect(result.user).toEqual(mockUser);
      expect(result.profile).toEqual(mockProfile);
      expect(result.salon).toEqual(mockSalon);
      expect(result.error).toBeNull();
    });

    it('should handle sign in errors', async () => {
      const mockError = { message: 'Invalid credentials' };

      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
        data: { user: null },
        error: mockError,
      });

      const result = await authService.signIn('<EMAIL>', 'wrongpassword');

      expect(result.user).toBeNull();
      expect(result.profile).toBeNull();
      expect(result.salon).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('createSalon', () => {
    it('should successfully create a salon', async () => {
      const mockSession = {
        user: { id: 'test-user-id' },
      };

      const mockSalon = {
        id: 'new-salon-id',
        name: 'New Salon',
        address: '456 New St',
        phone: '555-0456',
        email: '<EMAIL>',
        owner_id: 'test-user-id',
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: mockSession },
      });

      // Mock salon creation
      const salonInsertQuery = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockSalon,
              error: null,
            }),
          }),
        }),
      };

      // Mock profile update
      const profileUpdateQuery = {
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(salonInsertQuery)
        .mockReturnValueOnce(profileUpdateQuery);

      const result = await authService.createSalon({
        name: 'New Salon',
        address: '456 New St',
        phone: '555-0456',
        email: '<EMAIL>',
      });

      expect(result.salon).toEqual(mockSalon);
      expect(result.error).toBeNull();
    });

    it('should handle salon creation errors', async () => {
      const mockSession = {
        user: { id: 'test-user-id' },
      };

      const mockError = { message: 'Salon name already exists' };

      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: mockSession },
      });

      const salonInsertQuery = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: mockError,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(salonInsertQuery);

      const result = await authService.createSalon({
        name: 'Duplicate Salon',
      });

      expect(result.salon).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('getSession', () => {
    it('should return current session', async () => {
      const mockSession = {
        user: { id: 'test-user-id', email: '<EMAIL>' },
        access_token: 'mock-token',
      };

      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: mockSession },
      });

      const result = await authService.getSession();

      expect(result).toEqual(mockSession);
    });

    it('should return null when no session', async () => {
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: null },
      });

      const result = await authService.getSession();

      expect(result).toBeNull();
    });
  });

  describe('signOut', () => {
    it('should successfully sign out', async () => {
      (supabase.auth.signOut as jest.Mock).mockResolvedValue({
        error: null,
      });

      const result = await authService.signOut();

      expect(result.error).toBeNull();
      expect(supabase.auth.signOut).toHaveBeenCalled();
    });

    it('should handle sign out errors', async () => {
      const mockError = { message: 'Sign out failed' };

      (supabase.auth.signOut as jest.Mock).mockResolvedValue({
        error: mockError,
      });

      const result = await authService.signOut();

      expect(result.error).toEqual(mockError);
    });
  });
});
