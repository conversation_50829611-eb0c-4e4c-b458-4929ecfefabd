import { inventoryService } from '@/services/inventoryService';
import { supabase } from '@/config/supabase';

// Mock Supabase
jest.mock('@/config/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            data: [],
            error: null,
          })),
          single: jest.fn(() => ({
            data: null,
            error: null,
          })),
        })),
        order: jest.fn(() => ({
          data: [],
          error: null,
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(() => ({
          error: null,
        })),
      })),
      filter: jest.fn(() => ({
        order: jest.fn(() => ({
          data: [],
          error: null,
        })),
      })),
    })),
  },
}));

describe('InventoryService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBrands', () => {
    it('should successfully fetch brands', async () => {
      const mockBrands = [
        {
          id: 'brand-1',
          name: 'Wella',
          description: 'Professional hair color',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 'brand-2',
          name: "L'Oréal",
          description: 'Professional hair products',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const mockQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockBrands,
              error: null,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(mockQuery);

      const result = await inventoryService.getBrands();

      expect(result.data).toEqual(mockBrands);
      expect(result.error).toBeNull();
      expect(supabase.from).toHaveBeenCalledWith('brands');
    });

    it('should handle errors when fetching brands', async () => {
      const mockError = { message: 'Database connection failed' };

      const mockQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: null,
              error: mockError,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(mockQuery);

      const result = await inventoryService.getBrands();

      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('createBrand', () => {
    it('should successfully create a brand', async () => {
      const brandData = {
        name: 'New Brand',
        description: 'A new professional brand',
        is_active: true,
      };

      const mockCreatedBrand = {
        id: 'new-brand-id',
        ...brandData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const mockQuery = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCreatedBrand,
              error: null,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(mockQuery);

      const result = await inventoryService.createBrand(brandData);

      expect(result.data).toEqual(mockCreatedBrand);
      expect(result.error).toBeNull();
      expect(mockQuery.insert).toHaveBeenCalledWith(brandData);
    });
  });

  describe('getSalonInventory', () => {
    it('should successfully fetch salon inventory', async () => {
      const salonId = 'test-salon-id';
      const mockInventory = [
        {
          id: 'inventory-1',
          salon_id: salonId,
          product_id: 'product-1',
          current_stock: 10,
          min_stock: 5,
          max_stock: 50,
          cost_per_unit: 12.50,
          product: {
            id: 'product-1',
            name: 'Koleston Perfect 6/0',
            brand: {
              id: 'brand-1',
              name: 'Wella',
            },
          },
        },
      ];

      const mockQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockInventory,
              error: null,
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(mockQuery);

      const result = await inventoryService.getSalonInventory(salonId);

      expect(result.data).toEqual(mockInventory);
      expect(result.error).toBeNull();
      expect(supabase.from).toHaveBeenCalledWith('salon_inventory');
    });
  });

  describe('updateInventoryStock', () => {
    it('should successfully update stock with add operation', async () => {
      const salonId = 'test-salon-id';
      const productId = 'product-1';
      const stockChange = 5;

      const mockCurrentInventory = {
        current_stock: 10,
      };

      const mockUpdatedInventory = {
        id: 'inventory-1',
        salon_id: salonId,
        product_id: productId,
        current_stock: 15,
        min_stock: 5,
        max_stock: 50,
        product: {
          id: productId,
          name: 'Test Product',
        },
      };

      // Mock the current stock fetch
      const fetchQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentInventory,
              error: null,
            }),
          }),
        }),
      };

      // Mock the update query
      const updateQuery = {
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockUpdatedInventory,
                error: null,
              }),
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(fetchQuery)
        .mockReturnValueOnce(updateQuery);

      const result = await inventoryService.updateInventoryStock(
        salonId,
        productId,
        stockChange,
        'add'
      );

      expect(result.data).toEqual(mockUpdatedInventory);
      expect(result.error).toBeNull();
      expect(updateQuery.update).toHaveBeenCalledWith({
        current_stock: 15,
        last_restocked: expect.any(String),
      });
    });

    it('should successfully update stock with subtract operation', async () => {
      const salonId = 'test-salon-id';
      const productId = 'product-1';
      const stockChange = 3;

      const mockCurrentInventory = {
        current_stock: 10,
      };

      const mockUpdatedInventory = {
        id: 'inventory-1',
        salon_id: salonId,
        product_id: productId,
        current_stock: 7,
        min_stock: 5,
        max_stock: 50,
      };

      // Mock the current stock fetch
      const fetchQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentInventory,
              error: null,
            }),
          }),
        }),
      };

      // Mock the update query
      const updateQuery = {
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockUpdatedInventory,
                error: null,
              }),
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(fetchQuery)
        .mockReturnValueOnce(updateQuery);

      const result = await inventoryService.updateInventoryStock(
        salonId,
        productId,
        stockChange,
        'subtract'
      );

      expect(result.data).toEqual(mockUpdatedInventory);
      expect(result.error).toBeNull();
      expect(updateQuery.update).toHaveBeenCalledWith({
        current_stock: 7,
        last_restocked: undefined,
      });
    });

    it('should prevent negative stock when subtracting', async () => {
      const salonId = 'test-salon-id';
      const productId = 'product-1';
      const stockChange = 15; // More than current stock

      const mockCurrentInventory = {
        current_stock: 10,
      };

      const mockUpdatedInventory = {
        id: 'inventory-1',
        salon_id: salonId,
        product_id: productId,
        current_stock: 0, // Should not go below 0
        min_stock: 5,
        max_stock: 50,
      };

      // Mock the current stock fetch
      const fetchQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentInventory,
              error: null,
            }),
          }),
        }),
      };

      // Mock the update query
      const updateQuery = {
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockUpdatedInventory,
                error: null,
              }),
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(fetchQuery)
        .mockReturnValueOnce(updateQuery);

      const result = await inventoryService.updateInventoryStock(
        salonId,
        productId,
        stockChange,
        'subtract'
      );

      expect(result.data).toEqual(mockUpdatedInventory);
      expect(updateQuery.update).toHaveBeenCalledWith({
        current_stock: 0,
        last_restocked: undefined,
      });
    });
  });

  describe('getLowStockItems', () => {
    it('should successfully fetch low stock items', async () => {
      const salonId = 'test-salon-id';
      const mockLowStockItems = [
        {
          id: 'inventory-1',
          salon_id: salonId,
          product_id: 'product-1',
          current_stock: 2,
          min_stock: 5,
          max_stock: 50,
          product: {
            id: 'product-1',
            name: 'Low Stock Product',
            brand: {
              name: 'Test Brand',
            },
          },
        },
      ];

      const mockQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            filter: jest.fn().mockReturnValue({
              order: jest.fn().mockResolvedValue({
                data: mockLowStockItems,
                error: null,
              }),
            }),
          }),
        }),
      };

      (supabase.from as jest.Mock).mockReturnValue(mockQuery);

      const result = await inventoryService.getLowStockItems(salonId);

      expect(result.data).toEqual(mockLowStockItems);
      expect(result.error).toBeNull();
      expect(mockQuery.select).toHaveBeenCalledWith(expect.stringContaining('product'));
    });
  });
});
