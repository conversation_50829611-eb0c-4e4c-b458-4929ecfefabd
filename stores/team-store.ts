import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { TeamMember, TeamStore } from '@/types/team';

async function hashPassword(password: string): Promise<string> {
  const hash = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    password
  );
  return hash;
}

export const useTeamStore = create<TeamStore>()(
  persist(
    (set, get) => ({
      members: [],
      
      addMember: (memberData) => set((state) => ({
        members: [...state.members, {
          ...memberData,
          id: Date.now().toString(),
          joinedDate: new Date().toISOString(),
        }]
      })),
      
      updateMember: (id, updates) => set((state) => ({
        members: state.members.map((member) =>
          member.id === id ? { ...member, ...updates } : member
        )
      })),
      
      removeMember: (id) => set((state) => ({
        members: state.members.filter((member) => member.id !== id)
      })),
      
      toggleMemberStatus: (id) => set((state) => ({
        members: state.members.map((member) =>
          member.id === id 
            ? { ...member, status: member.status === 'active' ? 'inactive' : 'active' } 
            : member
        )
      })),
      
      getMemberByEmail: (email: string) => {
        const state = get();
        return state.members.find(member => 
          member.email.toLowerCase() === email.toLowerCase()
        );
      },
      
      getMembersBySalon: (salonId: string) => {
        const state = get();
        return state.members.filter(member => member.salonId === salonId);
      },
      
      verifyPassword: async (email: string, password: string) => {
        const member = get().getMemberByEmail(email);
        if (!member || !member.passwordHash) return false;
        
        const inputHash = await hashPassword(password);
        return inputHash === member.passwordHash;
      },
      
      hashPassword: hashPassword,
      
      updatePassword: (memberId: string, newPasswordHash: string) => set((state) => ({
        members: state.members.map((member) =>
          member.id === memberId ? { ...member, passwordHash: newPasswordHash } : member
        )
      })),
    }),
    {
      name: 'team-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);