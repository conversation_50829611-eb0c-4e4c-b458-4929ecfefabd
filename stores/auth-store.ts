import { create } from "zustand";
import { authService, Profile, Salon } from "@/services/authService";
import { supabase } from "@/config/supabase";
import { Session } from "@supabase/supabase-js";

interface User {
  id: string;
  email: string;
  name: string;
  isOwner: boolean;
  permissions?: string[];
  salonId: string | null;
}

interface BrandLineSelection {
  brandId: string;
  selectedLines: string[]; // Array of line IDs
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  profile: Profile | null;
  salon: Salon | null;
  session: Session | null;
  isLoading: boolean;
  preferredBrandLines: BrandLineSelection[];

  // Actions
  initialize: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, name: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  createSalon: (salonData: { name: string; address?: string; phone?: string; email?: string }) => Promise<{ success: boolean; error?: string }>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ success: boolean; error?: string }>;
  loadPreferredBrandLines: () => Promise<void>;
  updatePreferredBrandLines: (brandLines: BrandLineSelection[]) => Promise<void>;
  addBrandLineSelection: (brandId: string, lineIds: string[]) => Promise<void>;
  removeBrandLineSelection: (brandId: string) => Promise<void>;

  // Legacy compatibility
  login: (user: User) => Promise<void>;
  setIsAuthenticated: (value: boolean) => void;
  migrateToMultiUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => {
  console.log('🏗️ Creating auth store...');

  return {
    isAuthenticated: false,
    user: null,
    profile: null,
    salon: null,
    session: null,
    isLoading: true,
    preferredBrandLines: [],

  initialize: async () => {
    try {
      console.log('🔄 Initializing auth store...');
      set({ isLoading: true });

      // For now, just set loading to false to prevent blocking
      // The actual Supabase initialization will happen when needed
      set({ isLoading: false });

      console.log('✅ Auth store initialized (basic mode)');
    } catch (error) {
      console.error('Error initializing auth:', error);
      set({ isLoading: false });
    }
  },

  signIn: async (email, password) => {
    try {
      set({ isLoading: true });
      const result = await authService.signIn(email, password);

      if (result.error) {
        set({ isLoading: false });
        return { success: false, error: result.error.message };
      }

      // State will be updated by the auth state change listener
      set({ isLoading: false });
      return { success: true };
    } catch (error) {
      set({ isLoading: false });
      return { success: false, error: 'Error signing in' };
    }
  },

  signUp: async (email, password, name) => {
    try {
      set({ isLoading: true });
      const result = await authService.signUp(email, password, name);

      if (result.error) {
        set({ isLoading: false });
        return { success: false, error: result.error.message };
      }

      set({ isLoading: false });
      return { success: true };
    } catch (error) {
      set({ isLoading: false });
      return { success: false, error: 'Error signing up' };
    }
  },

  signOut: async () => {
    try {
      await authService.signOut();
      // State will be updated by the auth state change listener
    } catch (error) {
      console.error('Error signing out:', error);
    }
  },

  createSalon: async (salonData) => {
    try {
      const result = await authService.createSalon(salonData);

      if (result.error) {
        return { success: false, error: result.error.message || 'Error creating salon' };
      }

      // Refresh user data
      await get().initialize();
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Error creating salon' };
    }
  },

  updateProfile: async (updates) => {
    try {
      const result = await authService.updateProfile(updates);

      if (result.error) {
        return { success: false, error: result.error.message || 'Error updating profile' };
      }

      // Update local state
      const { user, profile } = get();
      if (user && profile) {
        const updatedProfile = { ...profile, ...updates };
        const updatedUser = {
          ...user,
          name: updatedProfile.name,
          isOwner: updatedProfile.is_owner,
          permissions: updatedProfile.permissions || [],
          salonId: updatedProfile.salon_id,
        };

        set({
          user: updatedUser,
          profile: updatedProfile,
        });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Error updating profile' };
    }
  },

  loadPreferredBrandLines: async () => {
    try {
      const { salon } = get();
      if (!salon) return;

      const { data, error } = await supabase
        .from('salon_brand_preferences')
        .select(`
          brand_id,
          product_line_ids,
          brands (
            id,
            name
          )
        `)
        .eq('salon_id', salon.id)
        .eq('is_preferred', true);

      if (error) {
        console.error('Error loading brand preferences:', error);
        return;
      }

      const brandLines: BrandLineSelection[] = data.map(pref => ({
        brandId: pref.brand_id,
        selectedLines: pref.product_line_ids || [],
      }));

      set({ preferredBrandLines: brandLines });
    } catch (error) {
      console.error('Error loading brand preferences:', error);
    }
  },

  updatePreferredBrandLines: async (brandLines) => {
    try {
      const { salon } = get();
      if (!salon) return;

      // Delete existing preferences
      await supabase
        .from('salon_brand_preferences')
        .delete()
        .eq('salon_id', salon.id);

      // Insert new preferences
      if (brandLines.length > 0) {
        const preferences = brandLines.map((bl, index) => ({
          salon_id: salon.id,
          brand_id: bl.brandId,
          product_line_ids: bl.selectedLines,
          is_preferred: true,
          priority_order: index + 1,
        }));

        const { error } = await supabase
          .from('salon_brand_preferences')
          .insert(preferences);

        if (error) {
          console.error('Error updating brand preferences:', error);
          return;
        }
      }

      set({ preferredBrandLines: brandLines });
    } catch (error) {
      console.error('Error updating brand preferences:', error);
    }
  },

  addBrandLineSelection: async (brandId, lineIds) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const existingIndex = preferredBrandLines.findIndex(bl => bl.brandId === brandId);

    let newBrandLines;
    if (existingIndex >= 0) {
      // Update existing brand selection
      newBrandLines = [...preferredBrandLines];
      newBrandLines[existingIndex] = { brandId, selectedLines: lineIds };
    } else {
      // Add new brand selection
      newBrandLines = [...preferredBrandLines, { brandId, selectedLines: lineIds }];
    }

    await updatePreferredBrandLines(newBrandLines);
  },

  removeBrandLineSelection: async (brandId) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const newBrandLines = preferredBrandLines.filter(bl => bl.brandId !== brandId);
    await updatePreferredBrandLines(newBrandLines);
  },

  // Legacy compatibility functions
  login: async (user) => {
    // For demo/legacy compatibility, just set the user state
    // In a real implementation, this would create a proper session
    console.warn('Using legacy login function. Consider migrating to signIn/signUp.');

    set({
      isAuthenticated: true,
      user,
      profile: {
        id: user.id,
        email: user.email,
        name: user.name,
        is_owner: user.isOwner,
        salon_id: user.salonId,
        permissions: user.permissions || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      salon: user.salonId ? {
        id: user.salonId,
        name: 'Demo Salon',
        address: null,
        phone: null,
        email: null,
        owner_id: user.id,
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } : null,
      isLoading: false,
    });
  },

  setIsAuthenticated: (value) => {
    set({ isAuthenticated: value });
  },

  migrateToMultiUser: async () => {
    const { user } = get();
    if (user && !user.id) {
      // Usuario antiguo sin los nuevos campos
      const migratedUser: User = {
        ...user,
        id: `user_${Date.now()}`,
        isOwner: true,
        permissions: undefined, // Owners no necesitan permisos específicos
        salonId: `salon_${Date.now()}`,
      };

      set({ user: migratedUser });
    }
  },
  };
});