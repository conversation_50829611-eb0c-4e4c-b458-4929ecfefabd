import { create } from "zustand";
import { supabase } from "@/config/supabase";
import { useAuthStore } from "./auth-store";
import {
  aiService,
  HairAnalysisRequest,
  HairAnalysisResult,
  FormulationRequest,
  FormulationResult
} from "@/services/aiService";
import {
  imageAnalysisService,
  HairImageAnalysis
} from "@/services/imageAnalysisService";

// Supabase hair analysis interface
interface HairAnalysis {
  id: string;
  client_id: string;
  salon_id: string;
  analyst_id: string;
  photo_urls: string[];
  photo_metadata: any;
  current_level: number;
  gray_percentage: number;
  hair_texture: string;
  hair_porosity: string;
  hair_condition: string;
  desired_level?: number;
  desired_tone?: string;
  desired_color_description?: string;
  ai_confidence: number;
  ai_recommendations: any;
  ai_warnings: string[];
  manual_adjustments: any;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface AIAnalysisStore {
  // Data
  analyses: HairAnalysis[];
  currentAnalysis: HairAnalysis | null;
  isLoading: boolean;
  error: string | null;

  // AI Service status
  isAIConfigured: boolean;
  availableServices: string[];

  // Actions
  initializeAI: () => Promise<void>;

  // Hair Analysis
  analyzeHairImage: (request: HairAnalysisRequest) => Promise<{ success: boolean; analysis?: HairAnalysisResult; error?: string }>;
  performImageAnalysis: (imageBase64: string) => Promise<{ success: boolean; analysis?: HairImageAnalysis; error?: string }>;

  // Formulation
  generateFormulation: (request: FormulationRequest) => Promise<{ success: boolean; formulation?: FormulationResult; error?: string }>;

  // Database operations
  saveAnalysis: (clientId: string, analysisData: Partial<HairAnalysis>) => Promise<{ success: boolean; error?: string }>;
  loadAnalyses: (clientId?: string) => Promise<void>;
  updateAnalysis: (analysisId: string, updates: Partial<HairAnalysis>) => Promise<{ success: boolean; error?: string }>;
  deleteAnalysis: (analysisId: string) => Promise<{ success: boolean; error?: string }>;

  // AI Configuration
  saveAIConfig: (serviceName: string, apiKey: string, settings?: any) => Promise<{ success: boolean; error?: string }>;
  getAIConfigs: () => Promise<{ success: boolean; configs?: any[]; error?: string }>;

  // Utility
  setCurrentAnalysis: (analysis: HairAnalysis | null) => void;
  clearError: () => void;
}

export const useAIAnalysisStore = create<AIAnalysisStore>((set, get) => ({
  // Initial state
  analyses: [],
  currentAnalysis: null,
  isLoading: false,
  error: null,
  isAIConfigured: false,
  availableServices: [],

  // Initialize AI services
  initializeAI: async () => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        set({ error: 'No salon selected' });
        return;
      }

      await aiService.initialize(salonId);
      await imageAnalysisService.initialize(salonId);

      // Check which services are configured
      const { success, configs } = await get().getAIConfigs();
      if (success && configs) {
        const availableServices = configs
          .filter(c => c.is_active)
          .map(c => c.service_name);

        set({
          isAIConfigured: availableServices.length > 0,
          availableServices
        });
      }
    } catch (error) {
      console.error('Error initializing AI:', error);
      set({ error: 'Failed to initialize AI services' });
    }
  },

  // Hair Analysis
  analyzeHairImage: async (request) => {
    try {
      set({ isLoading: true, error: null });

      const result = await aiService.analyzeHair(request);

      set({ isLoading: false });
      return { success: true, analysis: result };
    } catch (error) {
      console.error('Error analyzing hair:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Analysis failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Analysis failed' };
    }
  },

  performImageAnalysis: async (imageBase64) => {
    try {
      set({ isLoading: true, error: null });

      const result = await imageAnalysisService.analyzeHairImage(imageBase64);

      set({ isLoading: false });
      return { success: true, analysis: result };
    } catch (error) {
      console.error('Error performing image analysis:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Image analysis failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Image analysis failed' };
    }
  },

  // Formulation
  generateFormulation: async (request) => {
    try {
      set({ isLoading: true, error: null });

      const result = await aiService.generateFormulation(request);

      set({ isLoading: false });
      return { success: true, formulation: result };
    } catch (error) {
      console.error('Error generating formulation:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Formulation failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Formulation failed' };
    }
  },

  // Database operations
  saveAnalysis: async (clientId, analysisData) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;
      const userId = authStore.user?.id;

      if (!salonId || !userId) {
        return { success: false, error: 'No salon or user selected' };
      }

      const { data, error } = await supabase
        .from('hair_analyses')
        .insert({
          client_id: clientId,
          salon_id: salonId,
          analyst_id: userId,
          ...analysisData,
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      set((state) => ({
        analyses: [data, ...state.analyses],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error saving analysis:', error);
      return { success: false, error: 'Failed to save analysis' };
    }
  },

  loadAnalyses: async (clientId) => {
    try {
      set({ isLoading: true, error: null });

      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        set({ isLoading: false, error: 'No salon selected' });
        return;
      }

      let query = supabase
        .from('hair_analyses')
        .select('*')
        .eq('salon_id', salonId)
        .order('created_at', { ascending: false });

      if (clientId) {
        query = query.eq('client_id', clientId);
      }

      const { data, error } = await query;

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      set({ analyses: data || [], isLoading: false });
    } catch (error) {
      console.error('Error loading analyses:', error);
      set({ isLoading: false, error: 'Failed to load analyses' });
    }
  },

  updateAnalysis: async (analysisId, updates) => {
    try {
      const { error } = await supabase
        .from('hair_analyses')
        .update(updates)
        .eq('id', analysisId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Update local state
      set((state) => ({
        analyses: state.analyses.map((analysis) =>
          analysis.id === analysisId ? { ...analysis, ...updates } : analysis
        ),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error updating analysis:', error);
      return { success: false, error: 'Failed to update analysis' };
    }
  },

  deleteAnalysis: async (analysisId) => {
    try {
      const { error } = await supabase
        .from('hair_analyses')
        .delete()
        .eq('id', analysisId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Remove from local state
      set((state) => ({
        analyses: state.analyses.filter((analysis) => analysis.id !== analysisId),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error deleting analysis:', error);
      return { success: false, error: 'Failed to delete analysis' };
    }
  },

  // AI Configuration
  saveAIConfig: async (serviceName, apiKey, settings = {}) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      await aiService.saveAIConfig(salonId, serviceName, apiKey, settings);

      // Reinitialize AI services
      await get().initializeAI();

      return { success: true };
    } catch (error) {
      console.error('Error saving AI config:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to save AI config' };
    }
  },

  getAIConfigs: async () => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const configs = await aiService.getAIConfigs(salonId);
      return { success: true, configs };
    } catch (error) {
      console.error('Error getting AI configs:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to get AI configs' };
    }
  },

  // Utility
  setCurrentAnalysis: (analysis) => {
    set({ currentAnalysis: analysis });
  },

  clearError: () => {
    set({ error: null });
  },
}));

