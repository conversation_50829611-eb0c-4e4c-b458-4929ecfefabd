import { create } from "zustand";
import { supabase } from "@/config/supabase";
import { useAuthStore } from "./auth-store";
import {
  aiService,
  HairAnalysisRequest,
  HairAnalysisResult,
  FormulationRequest,
  FormulationResult
} from "@/services/aiService";
import {
  imageAnalysisService,
  HairImageAnalysis
} from "@/services/imageAnalysisService";

// Supabase hair analysis interface
interface HairAnalysis {
  id: string;
  client_id: string;
  salon_id: string;
  analyst_id: string;
  photo_urls: string[];
  photo_metadata: any;
  current_level: number;
  gray_percentage: number;
  hair_texture: string;
  hair_porosity: string;
  hair_condition: string;
  desired_level?: number;
  desired_tone?: string;
  desired_color_description?: string;
  ai_confidence: number;
  ai_recommendations: any;
  ai_warnings: string[];
  manual_adjustments: any;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface AIAnalysisStore {
  // Data
  analyses: HairAnalysis[];
  currentAnalysis: HairAnalysis | null;
  isLoading: boolean;
  error: string | null;

  // AI Service status
  isAIConfigured: boolean;
  availableServices: string[];

  // Actions
  initializeAI: () => Promise<void>;

  // Hair Analysis
  analyzeHairImage: (request: HairAnalysisRequest) => Promise<{ success: boolean; analysis?: HairAnalysisResult; error?: string }>;
  performImageAnalysis: (imageBase64: string) => Promise<{ success: boolean; analysis?: HairImageAnalysis; error?: string }>;

  // Formulation
  generateFormulation: (request: FormulationRequest) => Promise<{ success: boolean; formulation?: FormulationResult; error?: string }>;

  // Database operations
  saveAnalysis: (clientId: string, analysisData: Partial<HairAnalysis>) => Promise<{ success: boolean; error?: string }>;
  loadAnalyses: (clientId?: string) => Promise<void>;
  updateAnalysis: (analysisId: string, updates: Partial<HairAnalysis>) => Promise<{ success: boolean; error?: string }>;
  deleteAnalysis: (analysisId: string) => Promise<{ success: boolean; error?: string }>;

  // AI Configuration
  saveAIConfig: (serviceName: string, apiKey: string, settings?: any) => Promise<{ success: boolean; error?: string }>;
  getAIConfigs: () => Promise<{ success: boolean; configs?: any[]; error?: string }>;

  // Utility
  setCurrentAnalysis: (analysis: HairAnalysis | null) => void;
  clearError: () => void;
}

export const useAIAnalysisStore = create<AIAnalysisStore>((set, get) => ({
  // Initial state
  analyses: [],
  currentAnalysis: null,
  isLoading: false,
  error: null,
  isAIConfigured: false,
  availableServices: [],

  // Initialize AI services
  initializeAI: async () => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        set({ error: 'No salon selected' });
        return;
      }

      await aiService.initialize(salonId);
      await imageAnalysisService.initialize(salonId);

      // Check which services are configured
      const { success, configs } = await get().getAIConfigs();
      if (success && configs) {
        const availableServices = configs
          .filter(c => c.is_active)
          .map(c => c.service_name);

        set({
          isAIConfigured: availableServices.length > 0,
          availableServices
        });
      }
    } catch (error) {
      console.error('Error initializing AI:', error);
      set({ error: 'Failed to initialize AI services' });
    }
  },

  // Hair Analysis
  analyzeHairImage: async (request) => {
    try {
      set({ isLoading: true, error: null });

      const result = await aiService.analyzeHair(request);

      set({ isLoading: false });
      return { success: true, analysis: result };
    } catch (error) {
      console.error('Error analyzing hair:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Analysis failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Analysis failed' };
    }
  },

  performImageAnalysis: async (imageBase64) => {
    try {
      set({ isLoading: true, error: null });

      const result = await imageAnalysisService.analyzeHairImage(imageBase64);

      set({ isLoading: false });
      return { success: true, analysis: result };
    } catch (error) {
      console.error('Error performing image analysis:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Image analysis failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Image analysis failed' };
    }
  },

  // Formulation
  generateFormulation: async (request) => {
    try {
      set({ isLoading: true, error: null });

      const result = await aiService.generateFormulation(request);

      set({ isLoading: false });
      return { success: true, formulation: result };
    } catch (error) {
      console.error('Error generating formulation:', error);
      set({ isLoading: false, error: error instanceof Error ? error.message : 'Formulation failed' });
      return { success: false, error: error instanceof Error ? error.message : 'Formulation failed' };
    }
  },
  privacyMode: boolean;
  
  // Actions
  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (photoId: string, imageUri: string, currentLevel: number) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// Mock AI analysis function - In production, this would call your Supabase Edge Function
const performAIAnalysis = async (imageUri: string): Promise<AIAnalysisResult> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Mock complete hair analysis with professional precision
  const mockResult: AIAnalysisResult = {
    // General characteristics
    hairThickness: "Medio",
    hairDensity: "Media",
    overallTone: "Castaño Medio",
    overallUndertone: "Dorado",
    averageDepthLevel: 6.7,
    
    // Zone-specific analysis with decimal precision
    zoneAnalysis: {
      roots: {
        zone: "Raíces",
        depthLevel: 6.2,
        tone: "Castaño Medio",
        undertone: "Natural",
        state: "Natural",
        grayPercentage: 25,
        grayType: "Resistente/Vidriosa",
        grayPattern: "Placas",
        porosity: "Media",
        elasticity: "Buena",
        resistance: "Media",
        damage: "Bajo",
        cuticleState: "Lisa",
        confidence: 92
      },
      mids: {
        zone: "Medios",
        depthLevel: 6.8,
        tone: "Castaño Claro",
        undertone: "Dorado",
        state: "Teñido",
        porosity: "Media",
        elasticity: "Media",
        resistance: "Media",
        damage: "Medio",
        unwantedTone: "Naranja", // Detected orange tones
        pigmentAccumulation: "Media",
        cuticleState: "Áspera",
        demarkationBands: [{ location: 5, contrast: "Medio" }],
        confidence: 88
      },
      ends: {
        zone: "Puntas",
        depthLevel: 7.3,
        tone: "Rubio Oscuro",
        undertone: "Cenizo",
        state: "Decolorado",
        porosity: "Alta",
        elasticity: "Pobre",
        resistance: "Baja",
        damage: "Alto",
        unwantedTone: "Amarillo", // Detected yellow tones
        pigmentAccumulation: "Alta",
        cuticleState: "Dañada",
        confidence: 95
      }
    },
    
    // Chemical history detection
    detectedChemicalProcess: "Decoloración y coloración",
    estimatedLastProcessDate: "Hace 2-3 meses",
    detectedHomeRemedies: false,
    
    // Risk detection
    detectedRisks: {
      metalSalts: {
        detected: false,
        confidence: 85,
        signs: []
      },
      henna: {
        detected: false,
        confidence: 90,
        signs: []
      },
      extremeDamage: {
        detected: true,
        zones: ["Puntas"]
      }
    },
    
    // Service complexity based on multiple factors
    serviceComplexity: 'medium' as const,
    estimatedTime: 120, // 2 hours
    
    // Overall assessment
    overallCondition: "Cabello con tratamiento químico previo, diferencia notable entre zonas",
    recommendations: [
      "⚠️ Canas vidrosas detectadas: aplicar pre-ablandamiento o doble pigmentación",
      "Banda de demarcación a 5cm: aplicar técnica de difuminado gradual",
      "Igualación de color recomendada antes de nuevo proceso",
      "Neutralización de tonos naranjas en medios con matiz azul-violeta",
      "Neutralización de tonos amarillos en puntas con matiz violeta",
      "Alta acumulación de pigmento en puntas: considerar limpieza suave",
      "Cutícula dañada en puntas: tratamiento de reconstrucción obligatorio",
      "Usar fórmula diferenciada por zonas para resultado uniforme",
      "Considerar pre-pigmentación en zonas más claras",
      "Tiempo de procesamiento: Raíces 35min, Medios 25min, Puntas 15min"
    ],
    overallConfidence: 91,
    analysisTimestamp: Date.now()
  };
  
  return mockResult;
};

// Mock function to analyze desired color photos
const analyzeDesiredColorPhoto = async (photoId: string, imageUri: string, currentLevel: number): Promise<DesiredPhotoAnalysis> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock analysis based on current level to simulate viability
  const targetLevel = 8.3; // Mock detected level
  const levelDifference = Math.abs(targetLevel - currentLevel);
  
  const mockAnalysis: DesiredPhotoAnalysis = {
    photoId,
    detectedLevel: targetLevel,
    detectedTone: "Rubio Claro",
    detectedTechnique: "Balayage con babylights",
    detectedTones: ["Beige", "Perla", "Champagne"],
    viabilityScore: Math.max(0, 100 - (levelDifference * 15)), // Lower score for bigger jumps
    estimatedSessions: levelDifference > 3 ? 2 : 1,
    requiredProcesses: levelDifference > 3 
      ? ["Decoloración progresiva", "Matización", "Tratamiento reconstructor"]
      : ["Decoloración controlada", "Matización"],
    confidence: 88,
    warnings: levelDifference > 4 
      ? ["Diferencia de más de 4 niveles detectada", "Se recomienda proceso en múltiples sesiones"]
      : undefined
  };
  
  return mockAnalysis;
};

export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  // Initial state
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false, // Default to false for privacy
  },

  // Actions
  analyzeImage: async (imageUri: string) => {
    set({ isAnalyzing: true, analysisResult: null });
    
    try {
      // In production, this would:
      // 1. Upload image to Supabase Storage (temporary bucket)
      // 2. Trigger Edge Function for analysis
      // 3. Edge Function would blur faces and analyze with OpenAI
      // 4. Return results and delete images
      
      const result = await performAIAnalysis(imageUri);
      console.log("AI Analysis Store - Result received:", result);
      
      set(state => ({
        isAnalyzing: false,
        analysisResult: result,
        analysisHistory: state.settings.saveAnalysisHistory 
          ? [...state.analysisHistory, result]
          : state.analysisHistory
      }));
      
      // Save to AsyncStorage if history is enabled
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem(
            'salonier-analysis-history', 
            JSON.stringify(analysisHistory)
          );
        } catch (error) {
          console.error('Error saving analysis history:', error);
        }
      }
      
    } catch (error) {
      set({ isAnalyzing: false });
      throw error;
    }
  },

  analyzeDesiredPhoto: async (photoId: string, imageUri: string, currentLevel: number) => {
    set({ isAnalyzingDesiredPhoto: true });
    
    try {
      const analysis = await analyzeDesiredColorPhoto(photoId, imageUri, currentLevel);
      
      set(state => ({
        isAnalyzingDesiredPhoto: false,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis
        }
      }));
      
      return analysis;
    } catch (error) {
      set({ isAnalyzingDesiredPhoto: false });
      console.error('Error analyzing desired photo:', error);
      return null;
    }
  },

  clearAnalysis: () => {
    set({ analysisResult: null, desiredPhotoAnalyses: {} });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });
    
    try {
      await AsyncStorage.setItem(
        'salonier-ai-settings', 
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      console.error('Error saving AI settings:', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      console.error('Error clearing analysis history:', error);
    }
  }
}));

// Initialize store with saved data
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }
    
    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    console.error('Error initializing AI analysis store:', error);
  }
};

// Call initialization
initializeAIAnalysisStore();