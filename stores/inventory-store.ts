import { create } from 'zustand';
import { supabase } from '@/config/supabase';
import { useAuthStore } from './auth-store';
import {
  inventoryService,
  Brand,
  ProductLine,
  Product as SupabaseProduct,
  SalonInventory
} from '@/services/inventoryService';
import {
  Product,
  StockMovement,
  InventoryAlert,
  ConsumptionAnalysis,
  InventoryReport
} from '@/types/inventory';

interface InventoryStore {
  // Data
  brands: Brand[];
  productLines: ProductLine[];
  products: SupabaseProduct[];
  salonInventory: SalonInventory[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  isLoading: boolean;
  error: string | null;

  // Brand Actions
  loadBrands: () => Promise<void>;
  createBrand: (brandData: Omit<Brand, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; error?: string }>;

  // Product Line Actions
  loadProductLines: (brandId?: string) => Promise<void>;
  createProductLine: (lineData: Omit<ProductLine, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; error?: string }>;

  // Product Actions
  loadProducts: (filters?: { brandId?: string; productLineId?: string; productType?: string }) => Promise<void>;
  createProduct: (productData: Omit<SupabaseProduct, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; error?: string }>;

  // Inventory Actions
  loadSalonInventory: () => Promise<void>;
  addProductToInventory: (productId: string, initialStock?: number, minStock?: number, maxStock?: number, costPerUnit?: number) => Promise<{ success: boolean; error?: string }>;
  updateStock: (productId: string, stockChange: number, operation: 'add' | 'subtract' | 'set') => Promise<{ success: boolean; error?: string }>;
  removeProductFromInventory: (productId: string) => Promise<{ success: boolean; error?: string }>;
  getLowStockItems: () => Promise<{ success: boolean; data?: SalonInventory[]; error?: string }>;

  // Legacy compatibility methods (simplified)
  getProduct: (id: string) => SupabaseProduct | undefined;
  searchProducts: (query: string) => SupabaseProduct[];
  getProductsByCategory: (category: string) => SupabaseProduct[];
  getTotalInventoryValue: () => number;

  // Utility
  refreshAll: () => Promise<void>;
}

export const useInventoryStore = create<InventoryStore>((set, get) => ({
  // Initial state
  brands: [],
  productLines: [],
  products: [],
  salonInventory: [],
  movements: [],
  alerts: [],
  isLoading: false,
  error: null,

  // Brand Actions
  loadBrands: async () => {
    try {
      set({ isLoading: true, error: null });
      const { data, error } = await inventoryService.getBrands();

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      set({ brands: data || [], isLoading: false });
    } catch (error) {
      console.error('Error loading brands:', error);
      set({ isLoading: false, error: 'Error loading brands' });
    }
  },

  createBrand: async (brandData) => {
    try {
      const { data, error } = await inventoryService.createBrand(brandData);

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      set((state) => ({
        brands: [...state.brands, data!],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error creating brand:', error);
      return { success: false, error: 'Error creating brand' };
    }
  },

  // Product Line Actions
  loadProductLines: async (brandId) => {
    try {
      set({ isLoading: true, error: null });
      const { data, error } = await inventoryService.getProductLines(brandId);

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      set({ productLines: data || [], isLoading: false });
    } catch (error) {
      console.error('Error loading product lines:', error);
      set({ isLoading: false, error: 'Error loading product lines' });
    }
  },

  createProductLine: async (lineData) => {
    try {
      const { data, error } = await inventoryService.createProductLine(lineData);

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      set((state) => ({
        productLines: [...state.productLines, data!],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error creating product line:', error);
      return { success: false, error: 'Error creating product line' };
    }
  },

  // Product Actions
  loadProducts: async (filters) => {
    try {
      set({ isLoading: true, error: null });
      const { data, error } = await inventoryService.getProducts(filters);

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      set({ products: data || [], isLoading: false });
    } catch (error) {
      console.error('Error loading products:', error);
      set({ isLoading: false, error: 'Error loading products' });
    }
  },

  createProduct: async (productData) => {
    try {
      const { data, error } = await inventoryService.createProduct(productData);

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      set((state) => ({
        products: [...state.products, data!],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error creating product:', error);
      return { success: false, error: 'Error creating product' };
    }
  },

  // Inventory Actions
  loadSalonInventory: async () => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        set({ error: 'No salon selected' });
        return;
      }

      set({ isLoading: true, error: null });
      const { data, error } = await inventoryService.getSalonInventory(salonId);

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      set({ salonInventory: data || [], isLoading: false });
    } catch (error) {
      console.error('Error loading salon inventory:', error);
      set({ isLoading: false, error: 'Error loading salon inventory' });
    }
  },

  addProductToInventory: async (productId, initialStock = 0, minStock = 0, maxStock = 100, costPerUnit) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const { data, error } = await inventoryService.addProductToInventory(
        salonId,
        productId,
        initialStock,
        minStock,
        maxStock,
        costPerUnit
      );

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      set((state) => ({
        salonInventory: [...state.salonInventory, data!],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error adding product to inventory:', error);
      return { success: false, error: 'Error adding product to inventory' };
    }
  },
  updateStock: async (productId, stockChange, operation) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const { data, error } = await inventoryService.updateInventoryStock(
        salonId,
        productId,
        stockChange,
        operation
      );

      if (error) {
        return { success: false, error: error.message };
      }

      // Update local state
      set((state) => ({
        salonInventory: state.salonInventory.map((item) =>
          item.product_id === productId ? data! : item
        ),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error updating stock:', error);
      return { success: false, error: 'Error updating stock' };
    }
  },

  removeProductFromInventory: async (productId) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const { error } = await inventoryService.removeProductFromInventory(salonId, productId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Remove from local state
      set((state) => ({
        salonInventory: state.salonInventory.filter((item) => item.product_id !== productId),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error removing product from inventory:', error);
      return { success: false, error: 'Error removing product from inventory' };
    }
  },

  getLowStockItems: async () => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const { data, error } = await inventoryService.getLowStockItems(salonId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error getting low stock items:', error);
      return { success: false, error: 'Error getting low stock items' };
    }
  },

  // Legacy compatibility methods
  getProduct: (id) => {
    return get().products.find((p) => p.id === id);
  },

  searchProducts: (query) => {
    const normalizedQuery = query.toLowerCase().trim();
    return get().products.filter((p) =>
      p.name.toLowerCase().includes(normalizedQuery) ||
      p.brand?.name.toLowerCase().includes(normalizedQuery) ||
      p.product_type.toLowerCase().includes(normalizedQuery) ||
      (p.color_code && p.color_code.toLowerCase().includes(normalizedQuery))
    );
  },

  getProductsByCategory: (category) => {
    return get().products.filter((p) => p.product_type === category);
  },

  getTotalInventoryValue: () => {
    return get().salonInventory.reduce(
      (sum, item) => sum + (item.current_stock * (item.cost_per_unit || 0)),
      0
    );
  },

  refreshAll: async () => {
    const store = get();
    await Promise.all([
      store.loadBrands(),
      store.loadProductLines(),
      store.loadProducts(),
      store.loadSalonInventory(),
    ]);
  },
}));
