import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PricingConfiguration, SalonConfiguration } from '@/types/inventory';
import { RegionalConfig, CountryCode } from '@/types/regional';
import { getCountryByCode } from '@/mocks/countries-data';

interface SalonConfigStore {
  configuration: SalonConfiguration;
  regionalConfig: RegionalConfig | null;
  isInitialized: boolean;
  skipSafetyVerification: boolean;
  hasCompletedOnboarding: boolean;
  
  // Actions
  updateBusinessName: (name: string) => void;
  updateInventoryControlLevel: (level: 'solo-formulas' | 'smart-cost' | 'control-total') => void;
  updatePricing: (pricing: Partial<PricingConfiguration>) => void;
  updateNotifications: (notifications: Partial<SalonConfiguration['notifications']>) => void;
  setAutoConsumption: (enabled: boolean) => void;
  setRequireStockValidation: (enabled: boolean) => void;
  updateCountry: (countryCode: CountryCode) => void;
  updateMeasurementSystem: (system: 'metric' | 'imperial') => void;
  updateLanguage: (language: string) => void;
  setSkipSafetyVerification: (skip: boolean) => void;
  setHasCompletedOnboarding: (completed: boolean) => void;
  initializeDefaults: () => void;
  resetConfiguration: () => void;
  
  // Helpers
  formatCurrency: (amount: number) => string;
  applyMarkup: (cost: number) => number;
  roundPrice: (price: number) => number;
  formatWeight: (value: number, includeUnit?: boolean) => string;
  formatVolume: (value: number, includeUnit?: boolean) => string;
  convertVolume: (value: number, from: 'ml' | 'fl oz', to: 'ml' | 'fl oz') => number;
  convertWeight: (value: number, from: 'g' | 'oz', to: 'g' | 'oz') => number;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  getTerminology: (type: 'developer' | 'color') => string;
}

const defaultPricing: PricingConfiguration = {
  defaultMarkupPercentage: 300, // 300% markup (4x cost)
  roundingPolicy: 'nearest',
  roundingIncrement: 0.5,
  minimumServicePrice: 20,
  includeTaxInPrice: true,
  taxPercentage: 21, // IVA España
  currency: 'EUR',
  currencySymbol: '€',
  lastUpdated: new Date().toISOString(),
};

const defaultConfiguration: SalonConfiguration = {
  businessName: 'Mi Salón',
  inventoryControlLevel: 'smart-cost',
  pricing: defaultPricing,
  notifications: {
    lowStockAlerts: true,
    expirationAlerts: true,
    restockReminders: true,
  },
  autoConsumption: false,
  requireStockValidation: true,
  countryCode: 'ES',
  measurementSystem: 'metric',
  language: 'es',
};

export const useSalonConfigStore = create<SalonConfigStore>()(
  persist(
    (set, get) => ({
      configuration: defaultConfiguration,
      regionalConfig: getCountryByCode('ES')?.config || {
        countryCode: 'ES',
        countryName: 'España',
        region: 'Europe',
        measurementSystem: 'metric',
        currency: 'EUR',
        currencySymbol: '€',
        language: 'es',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        decimalSeparator: ',',
        thousandsSeparator: '.',
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        requiresAllergyTest: false,
        maxDeveloperVolume: 40,
      },
      isInitialized: false,
      skipSafetyVerification: false,
      hasCompletedOnboarding: false,

      updateBusinessName: (name) =>
        set((state) => ({
          configuration: { ...state.configuration, businessName: name },
        })),

      updateInventoryControlLevel: (level) =>
        set((state) => ({
          configuration: { ...state.configuration, inventoryControlLevel: level },
        })),

      updatePricing: (pricing) =>
        set((state) => ({
          configuration: {
            ...state.configuration,
            pricing: {
              ...state.configuration.pricing,
              ...pricing,
              lastUpdated: new Date().toISOString(),
            },
          },
        })),

      updateNotifications: (notifications) =>
        set((state) => ({
          configuration: {
            ...state.configuration,
            notifications: {
              ...state.configuration.notifications,
              ...notifications,
            },
          },
        })),

      setAutoConsumption: (enabled) =>
        set((state) => ({
          configuration: { ...state.configuration, autoConsumption: enabled },
        })),

      setRequireStockValidation: (enabled) =>
        set((state) => ({
          configuration: { ...state.configuration, requireStockValidation: enabled },
        })),

      setSkipSafetyVerification: (skip) =>
        set(() => ({
          skipSafetyVerification: skip,
        })),

      setHasCompletedOnboarding: (completed) =>
        set(() => ({
          hasCompletedOnboarding: completed,
        })),

      initializeDefaults: () =>
        set(() => ({
          configuration: defaultConfiguration,
          isInitialized: true,
        })),

      updateCountry: (countryCode) => {
        const countryInfo = getCountryByCode(countryCode);
        if (countryInfo) {
          set((state) => ({
            configuration: {
              ...state.configuration,
              countryCode,
              measurementSystem: countryInfo.config.measurementSystem,
              language: countryInfo.config.language,
            },
            regionalConfig: countryInfo.config,
          }));
          
          // Update pricing with new currency
          get().updatePricing({
            currency: countryInfo.config.currency,
            currencySymbol: countryInfo.config.currencySymbol,
          });
        }
      },

      updateMeasurementSystem: (system) =>
        set((state) => ({
          configuration: { ...state.configuration, measurementSystem: system },
          regionalConfig: state.regionalConfig ? {
            ...state.regionalConfig,
            measurementSystem: system,
            volumeUnit: system === 'metric' ? 'ml' : 'fl oz',
            weightUnit: system === 'metric' ? 'g' : 'oz',
          } : null,
        })),

      updateLanguage: (language) =>
        set((state) => ({
          configuration: { ...state.configuration, language },
          regionalConfig: state.regionalConfig ? {
            ...state.regionalConfig,
            language: language as any,
          } : null,
        })),

      resetConfiguration: () =>
        set(() => ({
          configuration: defaultConfiguration,
          regionalConfig: getCountryByCode('ES')?.config || null,
          isInitialized: false,
          skipSafetyVerification: false,
          hasCompletedOnboarding: false,
        })),

      formatCurrency: (amount) => {
        const { currency, currencySymbol, includeTaxInPrice, taxPercentage } = get().configuration.pricing;
        
        let finalAmount = amount;
        if (!includeTaxInPrice) {
          finalAmount = amount * (1 + taxPercentage / 100);
        }
        
        const rounded = get().roundPrice(finalAmount);
        
        return new Intl.NumberFormat('es-ES', {
          style: 'currency',
          currency: currency,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(rounded).replace(currency, currencySymbol);
      },

      applyMarkup: (cost) => {
        const { defaultMarkupPercentage, minimumServicePrice } = get().configuration.pricing;
        const markedUpPrice = cost * (1 + defaultMarkupPercentage / 100);
        return Math.max(markedUpPrice, minimumServicePrice);
      },

      roundPrice: (price) => {
        const { roundingPolicy, roundingIncrement } = get().configuration.pricing;
        
        if (roundingPolicy === 'none') return price;
        
        const factor = 1 / roundingIncrement;
        
        switch (roundingPolicy) {
          case 'up':
            return Math.ceil(price * factor) / factor;
          case 'down':
            return Math.floor(price * factor) / factor;
          case 'nearest':
          default:
            return Math.round(price * factor) / factor;
        }
      },

      formatWeight: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'g' : ''}`;
        
        let displayValue = value;
        let unit = regional.weightUnit;
        
        if (regional.weightUnit === 'oz' && regional.measurementSystem === 'imperial') {
          // Convert grams to ounces (1g = 0.035274oz)
          displayValue = value * 0.035274;
          unit = 'oz';
        }
        
        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      formatVolume: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'ml' : ''}`;
        
        let displayValue = value;
        let unit = regional.volumeUnit;
        
        if (regional.volumeUnit === 'fl oz' && regional.measurementSystem === 'imperial') {
          // Convert ml to fl oz (1ml = 0.033814 fl oz)
          displayValue = value * 0.033814;
          unit = 'fl oz';
        }
        
        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      convertVolume: (value, from, to) => {
        if (from === to) return value;
        
        const ML_TO_FL_OZ = 0.033814;
        const FL_OZ_TO_ML = 29.5735;
        
        if (from === 'ml' && to === 'fl oz') {
          return value * ML_TO_FL_OZ;
        } else if (from === 'fl oz' && to === 'ml') {
          return value * FL_OZ_TO_ML;
        }
        
        return value;
      },

      convertWeight: (value, from, to) => {
        if (from === to) return value;
        
        const G_TO_OZ = 0.035274;
        const OZ_TO_G = 28.3495;
        
        if (from === 'g' && to === 'oz') {
          return value * G_TO_OZ;
        } else if (from === 'oz' && to === 'g') {
          return value * OZ_TO_G;
        }
        
        return value;
      },

      getUnitLabel: (type) => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'volume' ? 'ml' : 'g';
        
        if (type === 'volume') {
          return regional.volumeUnit;
        } else {
          return regional.weightUnit;
        }
      },

      getTerminology: (type) => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'developer' ? 'oxidante' : 'tinte';
        
        if (type === 'developer') {
          return regional.developerTerminology;
        } else {
          return regional.colorTerminology;
        }
      },
    }),
    {
      name: 'salon-config-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        if (state && !state.isInitialized) {
          state.isInitialized = true;
          // Initialize regional config if not set
          if (!state.regionalConfig) {
            const countryCode = state.configuration.countryCode || 'ES';
            const countryInfo = getCountryByCode(countryCode as CountryCode);
            if (countryInfo) {
              state.regionalConfig = countryInfo.config;
            } else {
              // Fallback to Spanish config if country not found
              state.regionalConfig = {
                countryCode: 'ES',
                countryName: 'España',
                region: 'Europe',
                measurementSystem: 'metric',
                currency: 'EUR',
                currencySymbol: '€',
                language: 'es',
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '24h',
                decimalSeparator: ',',
                thousandsSeparator: '.',
                volumeUnit: 'ml',
                weightUnit: 'g',
                developerTerminology: 'oxidante',
                colorTerminology: 'tinte',
                requiresAllergyTest: false,
                maxDeveloperVolume: 40,
              };
            }
          }
        }
      },
    }
  )
);