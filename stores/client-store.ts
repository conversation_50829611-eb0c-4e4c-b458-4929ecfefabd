import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { mockClients } from "@/mocks/data";

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  since: string;
  lastVisit?: string;
  notes?: string;
  lastAnalysis?: {
    date: string;
    currentLevel: string;
    grayPercentage: string;
    hairTexture: string;
    confidence: number;
  };
  riskLevel?: string;
  allergies?: string[];
  preferences?: any[];
  // New safety and history fields
  knownAllergies?: string;
  pregnancyOrNursing?: boolean;
  sensitiveSkin?: boolean;
  chemicalTreatments?: {
    henna?: boolean;
    chemicalStraightening?: boolean;
    keratin?: boolean;
  };
  acceptsReminders?: boolean;
  preferredContact?: 'whatsapp' | 'sms' | 'call';
}

interface ClientStore {
  clients: Client[];
  isLoading: boolean;
  
  // Actions
  loadClients: () => void;
  addClient: (client: Omit<Client, 'id' | 'since'>) => void;
  updateClient: (id: string, updates: Partial<Client>) => void;
  deleteClient: (id: string) => void;
  getClient: (id: string) => Client | undefined;
}

export const useClientStore = create<ClientStore>()(
  persist(
    (set, get) => ({
      clients: mockClients,
      isLoading: false,

      loadClients: () => {
        set({ isLoading: true });
        // In a real app, this would fetch from an API
        setTimeout(() => {
          set({ clients: mockClients, isLoading: false });
        }, 500);
      },

      addClient: (clientData) => {
        const newClient: Client = {
          ...clientData,
          id: Date.now().toString(),
          since: new Date().toLocaleDateString('es-ES', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
          }),
        };
        
        set((state) => ({
          clients: [...state.clients, newClient],
        }));
      },

      updateClient: (id, updates) => {
        set((state) => ({
          clients: state.clients.map((client) =>
            client.id === id ? { ...client, ...updates } : client
          ),
        }));
      },

      deleteClient: (id) => {
        set((state) => ({
          clients: state.clients.filter((client) => client.id !== id),
        }));
      },

      getClient: (id) => {
        const state = get();
        return state.clients.find((client) => client.id === id);
      },
    }),
    {
      name: "client-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);