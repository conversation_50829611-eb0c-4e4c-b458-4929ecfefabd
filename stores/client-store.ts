import { create } from "zustand";
import { supabase } from "@/config/supabase";
import { useAuthStore } from "./auth-store";

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  since: string;
  lastVisit?: string;
  notes?: string;
  lastAnalysis?: {
    date: string;
    currentLevel: string;
    grayPercentage: string;
    hairTexture: string;
    confidence: number;
  };
  riskLevel?: string;
  allergies?: string[];
  preferences?: any[];
  // New safety and history fields
  knownAllergies?: string;
  pregnancyOrNursing?: boolean;
  sensitiveSkin?: boolean;
  chemicalTreatments?: {
    henna?: boolean;
    chemicalStraightening?: boolean;
    keratin?: boolean;
  };
  acceptsReminders?: boolean;
  preferredContact?: 'whatsapp' | 'sms' | 'call';
}

interface ClientStore {
  clients: Client[];
  isLoading: boolean;
  error: string | null;

  // Actions
  loadClients: () => Promise<void>;
  addClient: (client: Omit<Client, 'id' | 'since'>) => Promise<{ success: boolean; error?: string }>;
  updateClient: (id: string, updates: Partial<Client>) => Promise<{ success: boolean; error?: string }>;
  deleteClient: (id: string) => Promise<{ success: boolean; error?: string }>;
  getClient: (id: string) => Client | undefined;
  refreshClients: () => Promise<void>;
}

export const useClientStore = create<ClientStore>((set, get) => ({
  clients: [],
  isLoading: false,
  error: null,

  loadClients: async () => {
    try {
      set({ isLoading: true, error: null });

      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        set({ isLoading: false, error: 'No salon selected' });
        return;
      }

      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('salon_id', salonId)
        .order('created_at', { ascending: false });

      if (error) {
        set({ isLoading: false, error: error.message });
        return;
      }

      // Transform data to match Client interface
      const clients: Client[] = data.map(client => ({
        id: client.id,
        name: client.name,
        email: client.email || '',
        phone: client.phone || '',
        since: new Date(client.created_at).toLocaleDateString('es-ES', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }),
        lastVisit: client.last_visit ? new Date(client.last_visit).toLocaleDateString('es-ES') : undefined,
        notes: client.notes || undefined,
        riskLevel: client.risk_level || undefined,
        allergies: client.allergies || [],
        preferences: client.preferences || [],
        knownAllergies: client.known_allergies || undefined,
        pregnancyOrNursing: client.pregnancy_or_nursing || false,
        sensitiveSkin: client.sensitive_skin || false,
        chemicalTreatments: client.chemical_treatments || {},
        acceptsReminders: client.accepts_reminders !== false,
        preferredContact: client.preferred_contact as 'whatsapp' | 'sms' | 'call' || 'whatsapp',
      }));

      set({ clients, isLoading: false });
    } catch (error) {
      console.error('Error loading clients:', error);
      set({ isLoading: false, error: 'Error loading clients' });
    }
  },

  addClient: async (clientData) => {
    try {
      const authStore = useAuthStore.getState();
      const salonId = authStore.salon?.id;

      if (!salonId) {
        return { success: false, error: 'No salon selected' };
      }

      const { data, error } = await supabase
        .from('clients')
        .insert({
          salon_id: salonId,
          name: clientData.name,
          email: clientData.email || null,
          phone: clientData.phone || null,
          notes: clientData.notes || null,
          risk_level: clientData.riskLevel || null,
          allergies: clientData.allergies || [],
          preferences: clientData.preferences || {},
          known_allergies: clientData.knownAllergies || null,
          pregnancy_or_nursing: clientData.pregnancyOrNursing || false,
          sensitive_skin: clientData.sensitiveSkin || false,
          chemical_treatments: clientData.chemicalTreatments || {},
          accepts_reminders: clientData.acceptsReminders !== false,
          preferred_contact: clientData.preferredContact || 'whatsapp',
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Add to local state
      const newClient: Client = {
        id: data.id,
        name: data.name,
        email: data.email || '',
        phone: data.phone || '',
        since: new Date(data.created_at).toLocaleDateString('es-ES', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }),
        lastVisit: data.last_visit ? new Date(data.last_visit).toLocaleDateString('es-ES') : undefined,
        notes: data.notes || undefined,
        riskLevel: data.risk_level || undefined,
        allergies: data.allergies || [],
        preferences: data.preferences || [],
        knownAllergies: data.known_allergies || undefined,
        pregnancyOrNursing: data.pregnancy_or_nursing || false,
        sensitiveSkin: data.sensitive_skin || false,
        chemicalTreatments: data.chemical_treatments || {},
        acceptsReminders: data.accepts_reminders !== false,
        preferredContact: data.preferred_contact as 'whatsapp' | 'sms' | 'call' || 'whatsapp',
      };

      set((state) => ({
        clients: [newClient, ...state.clients],
      }));

      return { success: true };
    } catch (error) {
      console.error('Error adding client:', error);
      return { success: false, error: 'Error adding client' };
    }
  },

  updateClient: async (id, updates) => {
    try {
      const { error } = await supabase
        .from('clients')
        .update({
          name: updates.name,
          email: updates.email || null,
          phone: updates.phone || null,
          notes: updates.notes || null,
          last_visit: updates.lastVisit ? new Date(updates.lastVisit).toISOString() : null,
          risk_level: updates.riskLevel || null,
          allergies: updates.allergies || [],
          preferences: updates.preferences || {},
          known_allergies: updates.knownAllergies || null,
          pregnancy_or_nursing: updates.pregnancyOrNursing || false,
          sensitive_skin: updates.sensitiveSkin || false,
          chemical_treatments: updates.chemicalTreatments || {},
          accepts_reminders: updates.acceptsReminders !== false,
          preferred_contact: updates.preferredContact || 'whatsapp',
        })
        .eq('id', id);

      if (error) {
        return { success: false, error: error.message };
      }

      // Update local state
      set((state) => ({
        clients: state.clients.map((client) =>
          client.id === id ? { ...client, ...updates } : client
        ),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error updating client:', error);
      return { success: false, error: 'Error updating client' };
    }
  },

  deleteClient: async (id) => {
    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', id);

      if (error) {
        return { success: false, error: error.message };
      }

      // Remove from local state
      set((state) => ({
        clients: state.clients.filter((client) => client.id !== id),
      }));

      return { success: true };
    } catch (error) {
      console.error('Error deleting client:', error);
      return { success: false, error: 'Error deleting client' };
    }
  },

  getClient: (id) => {
    const state = get();
    return state.clients.find((client) => client.id === id);
  },

  refreshClients: async () => {
    await get().loadClients();
  },
}));