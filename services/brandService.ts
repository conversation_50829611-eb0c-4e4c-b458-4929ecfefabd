import { supabase } from '@/config/supabase';

export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductLine {
  id: string;
  brand_id: string;
  name: string;
  description?: string;
  line_type?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  brand?: Brand;
}

export interface BrandWithLines {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  is_active: boolean;
  product_lines: ProductLine[];
}

class BrandService {
  // Get all active brands
  async getBrands(): Promise<{ data: Brand[] | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .select('*')
      .eq('is_active', true)
      .order('name');

    return { data, error };
  }

  // Get brands with their product lines
  async getBrandsWithLines(): Promise<{ data: BrandWithLines[] | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .select(`
        *,
        product_lines (
          id,
          name,
          description,
          line_type,
          is_active
        )
      `)
      .eq('is_active', true)
      .eq('product_lines.is_active', true)
      .order('name');

    return { data, error };
  }

  // Get product lines for a specific brand
  async getProductLines(brandId: string): Promise<{ data: ProductLine[] | null; error: any }> {
    const { data, error } = await supabase
      .from('product_lines')
      .select('*')
      .eq('brand_id', brandId)
      .eq('is_active', true)
      .order('name');

    return { data, error };
  }

  // Find brand by name (for migration purposes)
  async findBrandByName(name: string): Promise<{ data: Brand | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .select('*')
      .ilike('name', name)
      .eq('is_active', true)
      .single();

    return { data, error };
  }

  // Find product line by name and brand
  async findProductLineByName(
    brandId: string, 
    lineName: string
  ): Promise<{ data: ProductLine | null; error: any }> {
    const { data, error } = await supabase
      .from('product_lines')
      .select('*')
      .eq('brand_id', brandId)
      .ilike('name', lineName)
      .eq('is_active', true)
      .single();

    return { data, error };
  }

  // Convert legacy brand names to UUIDs
  async migrateBrandNames(brandNames: string[]): Promise<{ [key: string]: string }> {
    const brandMap: { [key: string]: string } = {};

    for (const brandName of brandNames) {
      try {
        const { data: brand } = await this.findBrandByName(brandName);
        if (brand) {
          brandMap[brandName.toLowerCase()] = brand.id;
        }
      } catch (error) {
        console.warn(`Could not find brand: ${brandName}`);
      }
    }

    return brandMap;
  }

  // Get popular brand combinations for salons
  async getPopularBrandCombinations(): Promise<{ data: any[] | null; error: any }> {
    const { data, error } = await supabase
      .from('salon_brand_preferences')
      .select(`
        brand_id,
        brands (
          name,
          description
        )
      `)
      .eq('is_preferred', true)
      .limit(10);

    return { data, error };
  }

  // Create a new brand (admin function)
  async createBrand(brandData: {
    name: string;
    description?: string;
    logo_url?: string;
  }): Promise<{ data: Brand | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .insert({
        ...brandData,
        is_active: true,
      })
      .select()
      .single();

    return { data, error };
  }

  // Create a new product line
  async createProductLine(lineData: {
    brand_id: string;
    name: string;
    description?: string;
    line_type?: string;
  }): Promise<{ data: ProductLine | null; error: any }> {
    const { data, error } = await supabase
      .from('product_lines')
      .insert({
        ...lineData,
        is_active: true,
      })
      .select()
      .single();

    return { data, error };
  }

  // Search brands and product lines
  async searchBrands(query: string): Promise<{ data: BrandWithLines[] | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .select(`
        *,
        product_lines (
          id,
          name,
          description,
          line_type,
          is_active
        )
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .eq('product_lines.is_active', true)
      .order('name');

    return { data, error };
  }

  // Get brand statistics
  async getBrandStats(): Promise<{ data: any; error: any }> {
    try {
      const { data: totalBrands, error: brandsError } = await supabase
        .from('brands')
        .select('count')
        .eq('is_active', true);

      const { data: totalLines, error: linesError } = await supabase
        .from('product_lines')
        .select('count')
        .eq('is_active', true);

      const { data: popularBrands, error: popularError } = await supabase
        .from('salon_brand_preferences')
        .select(`
          brand_id,
          brands (name),
          count:salon_id
        `)
        .eq('is_preferred', true)
        .limit(5);

      if (brandsError || linesError || popularError) {
        return { 
          data: null, 
          error: brandsError || linesError || popularError 
        };
      }

      return {
        data: {
          totalBrands: totalBrands?.[0]?.count || 0,
          totalProductLines: totalLines?.[0]?.count || 0,
          popularBrands: popularBrands || [],
        },
        error: null,
      };
    } catch (error) {
      return { data: null, error };
    }
  }
}

export const brandService = new BrandService();
