import { supabase } from '@/config/supabase';

export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductLine {
  id: string;
  brand_id: string;
  name: string;
  description?: string;
  line_type?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  brand?: Brand;
}

export interface Product {
  id: string;
  brand_id: string;
  product_line_id?: string;
  name: string;
  product_type: string;
  color_code?: string;
  volume_strength?: string;
  size_ml?: number;
  price?: number;
  cost?: number;
  description?: string;
  ingredients?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  brand?: Brand;
  product_line?: ProductLine;
}

export interface SalonInventory {
  id: string;
  salon_id: string;
  product_id: string;
  current_stock: number;
  min_stock: number;
  max_stock: number;
  cost_per_unit?: number;
  last_restocked?: string;
  created_at: string;
  updated_at: string;
  product?: Product;
}

class InventoryService {
  // Brands
  async getBrands(): Promise<{ data: Brand[] | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .select('*')
      .eq('is_active', true)
      .order('name');

    return { data, error };
  }

  async createBrand(brandData: Omit<Brand, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: Brand | null; error: any }> {
    const { data, error } = await supabase
      .from('brands')
      .insert(brandData)
      .select()
      .single();

    return { data, error };
  }

  // Product Lines
  async getProductLines(brandId?: string): Promise<{ data: ProductLine[] | null; error: any }> {
    let query = supabase
      .from('product_lines')
      .select(`
        *,
        brand:brands(*)
      `)
      .eq('is_active', true);

    if (brandId) {
      query = query.eq('brand_id', brandId);
    }

    const { data, error } = await query.order('name');
    return { data, error };
  }

  async createProductLine(lineData: Omit<ProductLine, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: ProductLine | null; error: any }> {
    const { data, error } = await supabase
      .from('product_lines')
      .insert(lineData)
      .select(`
        *,
        brand:brands(*)
      `)
      .single();

    return { data, error };
  }

  // Products
  async getProducts(filters?: {
    brandId?: string;
    productLineId?: string;
    productType?: string;
  }): Promise<{ data: Product[] | null; error: any }> {
    let query = supabase
      .from('products')
      .select(`
        *,
        brand:brands(*),
        product_line:product_lines(*)
      `)
      .eq('is_active', true);

    if (filters?.brandId) {
      query = query.eq('brand_id', filters.brandId);
    }
    if (filters?.productLineId) {
      query = query.eq('product_line_id', filters.productLineId);
    }
    if (filters?.productType) {
      query = query.eq('product_type', filters.productType);
    }

    const { data, error } = await query.order('name');
    return { data, error };
  }

  async createProduct(productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: Product | null; error: any }> {
    const { data, error } = await supabase
      .from('products')
      .insert(productData)
      .select(`
        *,
        brand:brands(*),
        product_line:product_lines(*)
      `)
      .single();

    return { data, error };
  }

  // Salon Inventory
  async getSalonInventory(salonId: string): Promise<{ data: SalonInventory[] | null; error: any }> {
    const { data, error } = await supabase
      .from('salon_inventory')
      .select(`
        *,
        product:products(
          *,
          brand:brands(*),
          product_line:product_lines(*)
        )
      `)
      .eq('salon_id', salonId)
      .order('current_stock', { ascending: true });

    return { data, error };
  }

  async updateInventoryStock(
    salonId: string,
    productId: string,
    stockChange: number,
    operation: 'add' | 'subtract' | 'set'
  ): Promise<{ data: SalonInventory | null; error: any }> {
    // First, get current stock
    const { data: currentInventory, error: fetchError } = await supabase
      .from('salon_inventory')
      .select('current_stock')
      .eq('salon_id', salonId)
      .eq('product_id', productId)
      .single();

    if (fetchError) {
      return { data: null, error: fetchError };
    }

    let newStock: number;
    switch (operation) {
      case 'add':
        newStock = currentInventory.current_stock + stockChange;
        break;
      case 'subtract':
        newStock = Math.max(0, currentInventory.current_stock - stockChange);
        break;
      case 'set':
        newStock = stockChange;
        break;
    }

    const { data, error } = await supabase
      .from('salon_inventory')
      .update({ 
        current_stock: newStock,
        last_restocked: operation === 'add' ? new Date().toISOString() : undefined
      })
      .eq('salon_id', salonId)
      .eq('product_id', productId)
      .select(`
        *,
        product:products(
          *,
          brand:brands(*),
          product_line:product_lines(*)
        )
      `)
      .single();

    return { data, error };
  }

  async addProductToInventory(
    salonId: string,
    productId: string,
    initialStock: number = 0,
    minStock: number = 0,
    maxStock: number = 100,
    costPerUnit?: number
  ): Promise<{ data: SalonInventory | null; error: any }> {
    const { data, error } = await supabase
      .from('salon_inventory')
      .insert({
        salon_id: salonId,
        product_id: productId,
        current_stock: initialStock,
        min_stock: minStock,
        max_stock: maxStock,
        cost_per_unit: costPerUnit,
        last_restocked: initialStock > 0 ? new Date().toISOString() : null,
      })
      .select(`
        *,
        product:products(
          *,
          brand:brands(*),
          product_line:product_lines(*)
        )
      `)
      .single();

    return { data, error };
  }

  async removeProductFromInventory(
    salonId: string,
    productId: string
  ): Promise<{ error: any }> {
    const { error } = await supabase
      .from('salon_inventory')
      .delete()
      .eq('salon_id', salonId)
      .eq('product_id', productId);

    return { error };
  }

  async getLowStockItems(salonId: string): Promise<{ data: SalonInventory[] | null; error: any }> {
    const { data, error } = await supabase
      .from('salon_inventory')
      .select(`
        *,
        product:products(
          *,
          brand:brands(*),
          product_line:product_lines(*)
        )
      `)
      .eq('salon_id', salonId)
      .filter('current_stock', 'lte', 'min_stock')
      .order('current_stock', { ascending: true });

    return { data, error };
  }
}

export const inventoryService = new InventoryService();
