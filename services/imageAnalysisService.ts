import { supabase } from '@/config/supabase';

export interface ColorAnalysis {
  dominantColors: Array<{
    color: string;
    percentage: number;
    hex: string;
  }>;
  brightness: number;
  contrast: number;
  saturation: number;
}

export interface HairImageAnalysis {
  colorAnalysis: ColorAnalysis;
  estimatedLevel: number;
  estimatedGrayPercentage: number;
  textureIndicators: {
    fineness: number; // 0-1 scale
    curlPattern: 'straight' | 'wavy' | 'curly' | 'coily';
  };
  qualityMetrics: {
    sharpness: number;
    lighting: 'poor' | 'fair' | 'good' | 'excellent';
    angle: 'poor' | 'fair' | 'good' | 'excellent';
  };
  confidence: number;
}

class ImageAnalysisService {
  private salonId: string | null = null;

  async initialize(salonId: string) {
    this.salonId = salonId;
  }

  async analyzeHairImage(imageBase64: string): Promise<HairImageAnalysis> {
    try {
      // For now, we'll implement a basic client-side analysis
      // In production, you might want to use Google Vision API or similar
      const analysis = await this.performBasicImageAnalysis(imageBase64);
      return analysis;
    } catch (error) {
      console.error('Error analyzing hair image:', error);
      throw new Error('Failed to analyze hair image');
    }
  }

  private async performBasicImageAnalysis(imageBase64: string): Promise<HairImageAnalysis> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          throw new Error('Could not get canvas context');
        }

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const analysis = this.analyzeImageData(imageData);
        
        resolve(analysis);
      };
      
      img.src = `data:image/jpeg;base64,${imageBase64}`;
    });
  }

  private analyzeImageData(imageData: ImageData): HairImageAnalysis {
    const data = imageData.data;
    const pixels = data.length / 4;
    
    // Color analysis
    const colorCounts = new Map<string, number>();
    let totalBrightness = 0;
    let totalSaturation = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // Calculate brightness
      const brightness = (r * 0.299 + g * 0.587 + b * 0.114) / 255;
      totalBrightness += brightness;
      
      // Calculate saturation
      const max = Math.max(r, g, b) / 255;
      const min = Math.min(r, g, b) / 255;
      const saturation = max === 0 ? 0 : (max - min) / max;
      totalSaturation += saturation;
      
      // Group similar colors
      const colorKey = this.getColorGroup(r, g, b);
      colorCounts.set(colorKey, (colorCounts.get(colorKey) || 0) + 1);
    }
    
    const avgBrightness = totalBrightness / pixels;
    const avgSaturation = totalSaturation / pixels;
    
    // Get dominant colors
    const dominantColors = Array.from(colorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([color, count]) => ({
        color: this.getColorName(color),
        percentage: (count / pixels) * 100,
        hex: color,
      }));
    
    // Estimate hair level based on brightness
    const estimatedLevel = Math.round(avgBrightness * 10) + 1;
    
    // Estimate gray percentage based on saturation
    const estimatedGrayPercentage = Math.round((1 - avgSaturation) * 100);
    
    // Basic quality metrics
    const sharpness = this.calculateSharpness(imageData);
    const lighting = this.assessLighting(avgBrightness);
    
    return {
      colorAnalysis: {
        dominantColors,
        brightness: avgBrightness,
        contrast: this.calculateContrast(imageData),
        saturation: avgSaturation,
      },
      estimatedLevel: Math.min(Math.max(estimatedLevel, 1), 10),
      estimatedGrayPercentage: Math.min(Math.max(estimatedGrayPercentage, 0), 100),
      textureIndicators: {
        fineness: this.estimateFineness(imageData),
        curlPattern: this.estimateCurlPattern(imageData),
      },
      qualityMetrics: {
        sharpness,
        lighting,
        angle: 'fair', // Would need more sophisticated analysis
      },
      confidence: this.calculateConfidence(sharpness, lighting, avgBrightness),
    };
  }

  private getColorGroup(r: number, g: number, b: number): string {
    // Simplify colors into groups
    const rGroup = Math.floor(r / 32) * 32;
    const gGroup = Math.floor(g / 32) * 32;
    const bGroup = Math.floor(b / 32) * 32;
    
    return `#${rGroup.toString(16).padStart(2, '0')}${gGroup.toString(16).padStart(2, '0')}${bGroup.toString(16).padStart(2, '0')}`;
  }

  private getColorName(hex: string): string {
    // Basic color name mapping
    const colorMap: { [key: string]: string } = {
      '#000000': 'Black',
      '#202020': 'Very Dark Brown',
      '#404040': 'Dark Brown',
      '#606060': 'Medium Brown',
      '#808080': 'Light Brown',
      '#a0a0a0': 'Dark Blonde',
      '#c0c0c0': 'Medium Blonde',
      '#e0e0e0': 'Light Blonde',
      '#ffffff': 'Platinum/White',
    };
    
    return colorMap[hex] || 'Unknown';
  }

  private calculateSharpness(imageData: ImageData): number {
    // Simple edge detection for sharpness
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    let edgeSum = 0;
    let edgeCount = 0;
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const i = (y * width + x) * 4;
        const current = data[i];
        const right = data[i + 4];
        const bottom = data[(y + 1) * width * 4 + x * 4];
        
        const edgeStrength = Math.abs(current - right) + Math.abs(current - bottom);
        edgeSum += edgeStrength;
        edgeCount++;
      }
    }
    
    const avgEdgeStrength = edgeSum / edgeCount;
    return Math.min(avgEdgeStrength / 50, 1); // Normalize to 0-1
  }

  private calculateContrast(imageData: ImageData): number {
    const data = imageData.data;
    let min = 255;
    let max = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      const brightness = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
      min = Math.min(min, brightness);
      max = Math.max(max, brightness);
    }
    
    return (max - min) / 255;
  }

  private assessLighting(brightness: number): 'poor' | 'fair' | 'good' | 'excellent' {
    if (brightness < 0.2 || brightness > 0.9) return 'poor';
    if (brightness < 0.3 || brightness > 0.8) return 'fair';
    if (brightness < 0.4 || brightness > 0.7) return 'good';
    return 'excellent';
  }

  private estimateFineness(imageData: ImageData): number {
    // Simplified texture analysis based on high-frequency content
    const sharpness = this.calculateSharpness(imageData);
    return Math.min(sharpness * 2, 1); // Higher sharpness might indicate finer hair
  }

  private estimateCurlPattern(imageData: ImageData): 'straight' | 'wavy' | 'curly' | 'coily' {
    // This would require more sophisticated pattern recognition
    // For now, return a default
    return 'straight';
  }

  private calculateConfidence(
    sharpness: number,
    lighting: 'poor' | 'fair' | 'good' | 'excellent',
    brightness: number
  ): number {
    let confidence = 0.5; // Base confidence
    
    // Adjust based on sharpness
    confidence += sharpness * 0.3;
    
    // Adjust based on lighting
    const lightingScores = { poor: 0, fair: 0.1, good: 0.2, excellent: 0.3 };
    confidence += lightingScores[lighting];
    
    // Adjust based on brightness (prefer mid-range)
    const brightnessScore = 1 - Math.abs(brightness - 0.5) * 2;
    confidence += brightnessScore * 0.2;
    
    return Math.min(Math.max(confidence, 0), 1);
  }

  async saveImageAnalysis(
    salonId: string,
    clientId: string,
    imageUrl: string,
    analysis: HairImageAnalysis
  ) {
    const { error } = await supabase
      .from('hair_analyses')
      .insert({
        salon_id: salonId,
        client_id: clientId,
        photo_urls: [imageUrl],
        current_level: analysis.estimatedLevel,
        gray_percentage: analysis.estimatedGrayPercentage,
        ai_confidence: analysis.confidence,
        ai_recommendations: {
          colorAnalysis: analysis.colorAnalysis,
          textureIndicators: analysis.textureIndicators,
          qualityMetrics: analysis.qualityMetrics,
        },
      });

    if (error) {
      throw new Error(`Failed to save image analysis: ${error.message}`);
    }
  }
}

export const imageAnalysisService = new ImageAnalysisService();
