import { supabase } from '@/config/supabase';
import { authService } from './authService';

export interface SecurityEvent {
  event_type: string;
  description: string;
  user_id?: string;
  salon_id?: string;
  metadata?: any;
  timestamp: string;
}

export interface PermissionCheck {
  operation: string;
  resource?: string;
  resourceId?: string;
}

class SecurityService {
  // Log security events
  async logSecurityEvent(
    eventType: string,
    description: string,
    metadata: any = {}
  ): Promise<void> {
    try {
      const session = await authService.getSession();
      const profile = session?.user ? await authService.getProfile(session.user.id) : null;

      await supabase.rpc('log_security_event', {
        event_type: eventType,
        event_description: description,
        user_id: session?.user?.id || null,
        salon_id: profile?.salon_id || null,
        metadata: metadata
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  // Validate user permissions
  async validatePermission(operation: string, targetSalonId?: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('validate_operation_permission', {
        operation_type: operation,
        target_salon_id: targetSalonId
      });

      if (error) {
        console.error('Permission validation error:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Permission validation failed:', error);
      return false;
    }
  }

  // Check if user can access specific resource
  async canAccessResource(
    resourceType: string,
    resourceId: string,
    operation: 'read' | 'write' | 'delete' = 'read'
  ): Promise<boolean> {
    try {
      const session = await authService.getSession();
      if (!session?.user) {
        return false;
      }

      const profile = await authService.getProfile(session.user.id);
      if (!profile) {
        return false;
      }

      // Owners can access everything in their salon
      if (profile.is_owner) {
        return true;
      }

      // Check specific resource permissions
      switch (resourceType) {
        case 'client':
          return this.validatePermission('manage_clients', profile.salon_id);
        
        case 'inventory':
          if (operation === 'read') {
            return this.validatePermission('view_inventory', profile.salon_id);
          }
          return this.validatePermission('manage_inventory', profile.salon_id);
        
        case 'formulation':
          // Users can always access their own formulations
          const { data: formulation } = await supabase
            .from('formulations')
            .select('formulator_id')
            .eq('id', resourceId)
            .single();
          
          if (formulation?.formulator_id === session.user.id) {
            return true;
          }
          
          return this.validatePermission('view_all_formulations', profile.salon_id);
        
        case 'analysis':
          // Users can always access analyses they created
          const { data: analysis } = await supabase
            .from('hair_analyses')
            .select('analyst_id')
            .eq('id', resourceId)
            .single();
          
          if (analysis?.analyst_id === session.user.id) {
            return true;
          }
          
          return this.validatePermission('view_all_analyses', profile.salon_id);
        
        default:
          return false;
      }
    } catch (error) {
      console.error('Resource access check failed:', error);
      return false;
    }
  }

  // Sanitize sensitive data before logging
  sanitizeForLogging(data: any): any {
    const sensitiveFields = [
      'password',
      'api_key',
      'api_key_encrypted',
      'token',
      'secret',
      'private_key',
      'credit_card',
      'ssn',
      'phone',
      'email'
    ];

    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sanitized = { ...data };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        if (typeof sanitized[field] === 'string' && sanitized[field].length > 0) {
          sanitized[field] = '***REDACTED***';
        }
      }
    }

    // Recursively sanitize nested objects
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    }

    return sanitized;
  }

  // Validate input data to prevent injection attacks
  validateInput(input: any, type: 'string' | 'number' | 'email' | 'uuid' | 'json'): boolean {
    if (input === null || input === undefined) {
      return false;
    }

    switch (type) {
      case 'string':
        return typeof input === 'string' && input.length > 0 && input.length < 10000;
      
      case 'number':
        return typeof input === 'number' && !isNaN(input) && isFinite(input);
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return typeof input === 'string' && emailRegex.test(input);
      
      case 'uuid':
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return typeof input === 'string' && uuidRegex.test(input);
      
      case 'json':
        try {
          if (typeof input === 'string') {
            JSON.parse(input);
          } else if (typeof input === 'object') {
            JSON.stringify(input);
          } else {
            return false;
          }
          return true;
        } catch {
          return false;
        }
      
      default:
        return false;
    }
  }

  // Rate limiting check (simple in-memory implementation)
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>();

  checkRateLimit(
    identifier: string,
    maxRequests: number = 100,
    windowMs: number = 60000 // 1 minute
  ): boolean {
    const now = Date.now();
    const key = identifier;
    
    const current = this.rateLimitMap.get(key);
    
    if (!current || now > current.resetTime) {
      // Reset or initialize
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      return true;
    }
    
    if (current.count >= maxRequests) {
      // Rate limit exceeded
      this.logSecurityEvent(
        'RATE_LIMIT_EXCEEDED',
        `Rate limit exceeded for identifier: ${identifier}`,
        { identifier, maxRequests, windowMs }
      );
      return false;
    }
    
    // Increment counter
    current.count++;
    this.rateLimitMap.set(key, current);
    return true;
  }

  // Clean up old rate limit entries
  cleanupRateLimit(): void {
    const now = Date.now();
    for (const [key, value] of this.rateLimitMap.entries()) {
      if (now > value.resetTime) {
        this.rateLimitMap.delete(key);
      }
    }
  }

  // Encrypt sensitive data before storing
  async encryptSensitiveData(data: string): Promise<string> {
    try {
      const { data: encrypted, error } = await supabase.rpc('encrypt_sensitive_data', {
        data: data
      });

      if (error) {
        throw error;
      }

      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt sensitive data');
    }
  }

  // Decrypt sensitive data when retrieving
  async decryptSensitiveData(encryptedData: string): Promise<string | null> {
    try {
      const { data: decrypted, error } = await supabase.rpc('decrypt_sensitive_data', {
        encrypted_data: encryptedData
      });

      if (error) {
        throw error;
      }

      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return null;
    }
  }

  // Get security audit logs
  async getSecurityLogs(
    salonId: string,
    limit: number = 100,
    eventType?: string
  ): Promise<SecurityEvent[]> {
    try {
      let query = supabase
        .from('audit_logs')
        .select('*')
        .eq('salon_id', salonId)
        .eq('table_name', 'security_events')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (eventType) {
        query = query.eq('action', eventType);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return (data || []).map(log => ({
        event_type: log.action,
        description: log.new_values?.description || '',
        user_id: log.user_id,
        salon_id: log.salon_id,
        metadata: log.new_values?.metadata || {},
        timestamp: log.created_at
      }));
    } catch (error) {
      console.error('Failed to get security logs:', error);
      return [];
    }
  }
}

export const securityService = new SecurityService();

// Clean up rate limit entries every 5 minutes
setInterval(() => {
  securityService.cleanupRateLimit();
}, 5 * 60 * 1000);
