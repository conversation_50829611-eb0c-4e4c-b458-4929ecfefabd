import { supabase } from '@/config/supabase';
import { Session, User, AuthError } from '@supabase/supabase-js';

export interface Profile {
  id: string;
  email: string;
  name: string;
  is_owner: boolean;
  salon_id: string | null;
  permissions: string[] | null;
  created_at: string;
  updated_at: string;
}

export interface Salon {
  id: string;
  name: string;
  address: string | null;
  phone: string | null;
  email: string | null;
  owner_id: string;
  settings: any;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User | null;
  profile: Profile | null;
  salon: Salon | null;
  error: AuthError | null;
}

class AuthService {
  // Sign up new user
  async signUp(email: string, password: string, name: string): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        return { user: null, profile: null, salon: null, error };
      }

      // Profile will be created automatically by the trigger
      const profile = await this.getProfile(data.user?.id);
      
      return {
        user: data.user,
        profile,
        salon: null,
        error: null,
      };
    } catch (error) {
      return {
        user: null,
        profile: null,
        salon: null,
        error: error as AuthError,
      };
    }
  }

  // Sign in existing user
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { user: null, profile: null, salon: null, error };
      }

      const profile = await this.getProfile(data.user?.id);
      const salon = profile?.salon_id ? await this.getSalon(profile.salon_id) : null;

      return {
        user: data.user,
        profile,
        salon,
        error: null,
      };
    } catch (error) {
      return {
        user: null,
        profile: null,
        salon: null,
        error: error as AuthError,
      };
    }
  }

  // Sign out
  async signOut(): Promise<{ error: AuthError | null }> {
    const { error } = await supabase.auth.signOut();
    return { error };
  }

  // Get current session
  async getSession(): Promise<Session | null> {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  }

  // Get user profile
  async getProfile(userId?: string): Promise<Profile | null> {
    if (!userId) {
      const session = await this.getSession();
      userId = session?.user?.id;
    }

    if (!userId) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }

    return data;
  }

  // Get salon information
  async getSalon(salonId: string): Promise<Salon | null> {
    const { data, error } = await supabase
      .from('salons')
      .select('*')
      .eq('id', salonId)
      .single();

    if (error) {
      console.error('Error fetching salon:', error);
      return null;
    }

    return data;
  }

  // Create salon (for new salon owners)
  async createSalon(salonData: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  }): Promise<{ salon: Salon | null; error: any }> {
    try {
      const session = await this.getSession();
      if (!session?.user) {
        return { salon: null, error: new Error('User not authenticated') };
      }

      const { data, error } = await supabase
        .from('salons')
        .insert({
          ...salonData,
          owner_id: session.user.id,
        })
        .select()
        .single();

      if (error) {
        return { salon: null, error };
      }

      // Update user profile to link to salon and make them owner
      await supabase
        .from('profiles')
        .update({
          salon_id: data.id,
          is_owner: true,
        })
        .eq('id', session.user.id);

      return { salon: data, error: null };
    } catch (error) {
      return { salon: null, error };
    }
  }

  // Update profile
  async updateProfile(updates: Partial<Profile>): Promise<{ profile: Profile | null; error: any }> {
    try {
      const session = await this.getSession();
      if (!session?.user) {
        return { profile: null, error: new Error('User not authenticated') };
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', session.user.id)
        .select()
        .single();

      if (error) {
        return { profile: null, error };
      }

      return { profile: data, error: null };
    } catch (error) {
      return { profile: null, error };
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  // Reset password
  async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    return { error };
  }

  // Update password
  async updatePassword(newPassword: string): Promise<{ error: AuthError | null }> {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });
    return { error };
  }
}

export const authService = new AuthService();
