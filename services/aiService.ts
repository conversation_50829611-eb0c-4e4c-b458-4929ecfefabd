import OpenAI from 'openai';
import { supabase } from '@/config/supabase';

export interface HairAnalysisRequest {
  imageBase64: string;
  currentLevel?: number;
  grayPercentage?: number;
  desiredLevel?: number;
  desiredTone?: string;
  hairTexture?: 'fine' | 'medium' | 'coarse';
  hairCondition?: 'excellent' | 'good' | 'fair' | 'poor';
  previousTreatments?: string[];
  allergies?: string[];
}

export interface HairAnalysisResult {
  currentLevel: number;
  grayPercentage: number;
  hairTexture: 'fine' | 'medium' | 'coarse';
  hairPorosity: 'low' | 'medium' | 'high';
  hairCondition: 'excellent' | 'good' | 'fair' | 'poor';
  confidence: number;
  recommendations: {
    technique: string;
    processingTime: number;
    warnings: string[];
    products: Array<{
      type: 'color' | 'developer' | 'treatment';
      name: string;
      quantity: number;
      notes?: string;
    }>;
  };
  analysis: string;
}

export interface FormulationRequest {
  currentLevel: number;
  desiredLevel: number;
  desiredTone: string;
  grayPercentage: number;
  hairTexture: 'fine' | 'medium' | 'coarse';
  hairCondition: 'excellent' | 'good' | 'fair' | 'poor';
  availableProducts: Array<{
    id: string;
    name: string;
    type: string;
    brand: string;
    colorCode?: string;
    volumeStrength?: string;
  }>;
  technique?: string;
}

export interface FormulationResult {
  formula: Array<{
    productId: string;
    productName: string;
    quantity: number;
    ratio?: number;
  }>;
  technique: string;
  processingTime: number;
  instructions: string[];
  warnings: string[];
  expectedResult: string;
  confidence: number;
}

class AIService {
  private openai: OpenAI | null = null;
  private salonId: string | null = null;

  async initialize(salonId: string) {
    this.salonId = salonId;
    
    // Get AI service configuration from Supabase
    const { data: configs } = await supabase
      .from('ai_service_configs')
      .select('*')
      .eq('salon_id', salonId)
      .eq('is_active', true);

    const openaiConfig = configs?.find(c => c.service_name === 'openai');
    
    if (openaiConfig?.api_key_encrypted) {
      // In a real app, you would decrypt the API key here
      // For now, we'll use the environment variable
      const apiKey = process.env.OPENAI_API_KEY || openaiConfig.api_key_encrypted;
      
      if (apiKey) {
        this.openai = new OpenAI({
          apiKey,
        });
      }
    }
  }

  async analyzeHair(request: HairAnalysisRequest): Promise<HairAnalysisResult> {
    if (!this.openai) {
      throw new Error('OpenAI not configured. Please add your API key in salon settings.');
    }

    try {
      const prompt = this.buildHairAnalysisPrompt(request);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'system',
            content: 'You are an expert hair colorist and analyst. Analyze hair photos and provide detailed technical assessments for professional salon use.'
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${request.imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      });

      const analysis = response.choices[0]?.message?.content;
      if (!analysis) {
        throw new Error('No analysis received from AI');
      }

      return this.parseHairAnalysis(analysis);
    } catch (error) {
      console.error('Error analyzing hair:', error);
      throw new Error('Failed to analyze hair. Please try again.');
    }
  }

  async generateFormulation(request: FormulationRequest): Promise<FormulationResult> {
    if (!this.openai) {
      throw new Error('OpenAI not configured. Please add your API key in salon settings.');
    }

    try {
      const prompt = this.buildFormulationPrompt(request);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are a master hair colorist with 20+ years of experience. Create precise color formulations using only the available products provided.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.2,
      });

      const formulation = response.choices[0]?.message?.content;
      if (!formulation) {
        throw new Error('No formulation received from AI');
      }

      return this.parseFormulation(formulation, request.availableProducts);
    } catch (error) {
      console.error('Error generating formulation:', error);
      throw new Error('Failed to generate formulation. Please try again.');
    }
  }

  private buildHairAnalysisPrompt(request: HairAnalysisRequest): string {
    return `
Analyze this hair photo and provide a detailed assessment. Consider:

Current Information:
- Current level: ${request.currentLevel || 'Unknown'}
- Gray percentage: ${request.grayPercentage || 'Unknown'}%
- Hair texture: ${request.hairTexture || 'Unknown'}
- Hair condition: ${request.hairCondition || 'Unknown'}
- Previous treatments: ${request.previousTreatments?.join(', ') || 'None specified'}
- Known allergies: ${request.allergies?.join(', ') || 'None specified'}

Desired Result:
- Target level: ${request.desiredLevel || 'Not specified'}
- Desired tone: ${request.desiredTone || 'Not specified'}

Please provide your analysis in this exact JSON format:
{
  "currentLevel": number (1-10),
  "grayPercentage": number (0-100),
  "hairTexture": "fine|medium|coarse",
  "hairPorosity": "low|medium|high",
  "hairCondition": "excellent|good|fair|poor",
  "confidence": number (0.0-1.0),
  "recommendations": {
    "technique": "string",
    "processingTime": number (minutes),
    "warnings": ["string"],
    "products": [{"type": "color|developer|treatment", "name": "string", "quantity": number, "notes": "string"}]
  },
  "analysis": "detailed text analysis"
}
`;
  }

  private buildFormulationPrompt(request: FormulationRequest): string {
    const productsText = request.availableProducts
      .map(p => `- ${p.name} (${p.type}) - Brand: ${p.brand}${p.colorCode ? `, Code: ${p.colorCode}` : ''}${p.volumeStrength ? `, Volume: ${p.volumeStrength}` : ''}`)
      .join('\n');

    return `
Create a precise hair color formulation with these specifications:

Hair Analysis:
- Current level: ${request.currentLevel}
- Desired level: ${request.desiredLevel}
- Desired tone: ${request.desiredTone}
- Gray percentage: ${request.grayPercentage}%
- Hair texture: ${request.hairTexture}
- Hair condition: ${request.hairCondition}
- Technique: ${request.technique || 'Standard application'}

Available Products:
${productsText}

Please provide your formulation in this exact JSON format:
{
  "formula": [{"productId": "string", "productName": "string", "quantity": number, "ratio": number}],
  "technique": "string",
  "processingTime": number,
  "instructions": ["step by step instructions"],
  "warnings": ["important warnings"],
  "expectedResult": "description of expected outcome",
  "confidence": number (0.0-1.0)
}

Use only the products listed above. Provide exact quantities in grams or milliliters.
`;
  }

  private parseHairAnalysis(analysis: string): HairAnalysisResult {
    try {
      // Try to extract JSON from the response
      const jsonMatch = analysis.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: create a basic analysis if JSON parsing fails
      return {
        currentLevel: 6,
        grayPercentage: 20,
        hairTexture: 'medium',
        hairPorosity: 'medium',
        hairCondition: 'good',
        confidence: 0.7,
        recommendations: {
          technique: 'Standard application',
          processingTime: 30,
          warnings: ['Manual analysis required - AI parsing failed'],
          products: []
        },
        analysis: analysis
      };
    } catch (error) {
      throw new Error('Failed to parse hair analysis result');
    }
  }

  private parseFormulation(formulation: string, availableProducts: FormulationRequest['availableProducts']): FormulationResult {
    try {
      // Try to extract JSON from the response
      const jsonMatch = formulation.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        // Validate that all products exist in available products
        const validatedFormula = parsed.formula.filter((item: any) => 
          availableProducts.some(p => p.id === item.productId || p.name === item.productName)
        );
        
        return {
          ...parsed,
          formula: validatedFormula
        };
      }
      
      throw new Error('No valid JSON found in formulation response');
    } catch (error) {
      throw new Error('Failed to parse formulation result');
    }
  }

  async saveAIConfig(salonId: string, serviceName: string, apiKey: string, settings: any = {}) {
    // In a real app, you would encrypt the API key before storing
    const { error } = await supabase
      .from('ai_service_configs')
      .upsert({
        salon_id: salonId,
        service_name: serviceName,
        api_key_encrypted: apiKey, // Should be encrypted
        settings,
        is_active: true,
      });

    if (error) {
      throw new Error(`Failed to save AI configuration: ${error.message}`);
    }
  }

  async getAIConfigs(salonId: string) {
    const { data, error } = await supabase
      .from('ai_service_configs')
      .select('service_name, settings, is_active')
      .eq('salon_id', salonId);

    if (error) {
      throw new Error(`Failed to get AI configurations: ${error.message}`);
    }

    return data || [];
  }
}

export const aiService = new AIService();
