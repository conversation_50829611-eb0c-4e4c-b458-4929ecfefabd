# Flujo de Trabajo Estándar para Claude Code

## Wizard de Seguridad (2025-07-06)

### Implementación del Wizard de Seguridad de 4 pasos
La aplicación incluye un wizard de seguridad completo antes de iniciar cualquier servicio de coloración.

#### Estructura:
1. **Paso 1 - Checklist de Seguridad**: Verificación de protocolos básicos
2. **Paso 2 - Test de Parche**: Verificación de alergias (48h antes)
3. **Paso 3 - Verificaciones Críticas**: Test de sales metálicas, henna, formol
4. **Paso 4 - Consentimiento**: Firma digital y aceptación de términos

#### Configuración:
- En `settings.tsx` existe un toggle "Saltar Verificación de Seguridad"
- Al activarlo, se muestra advertencia legal
- Si está activado, el flujo salta directo a `/service/new`
- Si está desactivado, pasa por `/service/safety-verification`

#### Datos críticos verificados:
- **Sales metálicas**: Test obligatorio con H₂O₂ si hay historial químico
- **Henna**: Incompatible con procesos químicos
- **Formaldehído**: Verificación de alisados previos
- **Remedios caseros**: Limón, manzanilla, vinagre, etc.

#### Integración:
- Los resultados se guardan en `consentRecord` del historial del cliente
- Incluye `criticalChecks` con todos los resultados de las verificaciones
- La firma digital se almacena como base64

## Lecciones Aprendidas

### Duplicación de tabs en diagnóstico (2025-07-08)
**Problema**: Los tabs de "Raíces", "Medios" y "Puntas" aparecían duplicados en la interfaz de diagnóstico
**Causa**: Código duplicado envuelto en un fragmento React innecesario
**Solución**: Eliminar el bloque de código duplicado (líneas 1880-1931 en `app/service/new.tsx`)

### Vistas sin funcionalidad (2025-07-08)
**Problema**: Botones "Vista Mapa" y "Vista Detallada" aparecían en la UI pero sin funcionalidad real
**Estado**: No se encontraron en el código actual - posiblemente ya eliminados o requieren recarga de la app
**Acción**: Si persisten después de recargar, investigar más a fondo

### Visualización de costes y rentabilidad (2025-07-08)
**Problema**: El desglose de costes y rentabilidad no aparecía al generar fórmulas
**Causa**: Se había modificado incorrectamente `calculateFormulaCost` para retornar `null` en "solo-formulas"
**Solución**: Restaurar el comportamiento original donde todos los niveles calculan costes
**Comportamiento actual**:
- "Solo Fórmulas": Muestra costes estimados (precios predefinidos)
- "Smart Cost": Muestra costes reales del inventario
- "Control Total": Muestra costes reales + verificación de stock

### Coherencia de niveles de inventario (2025-07-08)
**Ajuste realizado**: La verificación de stock ahora solo aparece en "Control Total"
**Comportamiento por nivel**:
1. **"Solo Fórmulas"**: 
   - Solo genera fórmulas
   - Muestra costes estimados
   - NO muestra verificación de stock
2. **"Smart Cost"**: 
   - Genera fórmulas
   - Calcula costes reales y rentabilidad
   - NO muestra verificación de stock
3. **"Control Total"**: 
   - Todo lo anterior
   - SÍ muestra verificación de stock
   - Permite consumir inventario

### expo-camera y CameraView (2025-07-05)
**Problema**: `The <CameraView> component does not support children`
**Solución**: Usar posicionamiento absoluto para overlays
```jsx
// ❌ INCORRECTO
<CameraView>
  <View>{/* contenido */}</View>
</CameraView>

// ✅ CORRECTO
<View style={styles.container}>
  <CameraView style={styles.camera} />
  <View style={StyleSheet.absoluteFillObject}>
    {/* overlay content */}
  </View>
</View>
```

### Unificación de Experiencia de Captura de Fotos (2025-07-08)
**Problema**: Inconsistencia entre las fases de "Color Actual" y "Color Deseado" en la captura de fotos
**Síntomas**:
- Color Actual: 3-5 fotos con captura guiada
- Color Deseado: Solo 1-3 fotos sin botones claros
- iOS mostraba modal confuso sobre cámara no disponible

**Solución implementada**:
1. **Detección unificada de plataforma**:
   - Constante `supportsGuidedCamera` en ambas fases
   - iOS usa cámara estándar, Android usa captura guiada
   - Sin modales confusos

2. **Límites de fotos consistentes**:
   - Ambas fases ahora permiten 3-5 fotos
   - Mismos botones de acción visibles

3. **Mejora de tipos de fotos para Color Deseado**:
   - Vista General (👁️) - Look completo
   - Técnica/Mechas (🎨) - Detalles de aplicación
   - Tono Natural (☀️) - Color real sin filtros
   - Contraste Raíces (🌱) - Transición natural
   - Dimensión/Movimiento (💫) - Profundidad del color

4. **Tooltips de ayuda**: Botón (?) en cada slot explicando qué capturar

**Archivos modificados**:
- `types/desired-photo.ts`: Nuevos tipos y guías mejoradas
- `app/service/new.tsx`: Detección de plataforma unificada
- `components/DesiredPhotoGallery.tsx`: UI consistente con PhotoGallery

### Consistencia de Interfaces de Producto (2025-07-09)
**Problema**: Existían dos interfaces diferentes para agregar productos
- Pantalla principal (`/app/inventory/new.tsx`) con entrada rápida IA
- Modal simplificado en `PricingSetupModal.tsx` sin IA

**Solución**: Unificar redirigiendo desde el modal a la pantalla principal
**Resultado**: Una sola interfaz consistente, siempre con acceso a funcionalidad IA

### Formulario de Nuevo Cliente Mejorado (2025-07-09)
**Problema**: Formulario básico solo con nombre, email, teléfono y notas

**Mejoras implementadas**:
1. **Sección de Seguridad** (🛡️):
   - Campo de alergias conocidas (PPD, amoníaco, níquel)
   - Switch embarazo/lactancia
   - Switch cuero cabelludo sensible

2. **Tratamientos Químicos** (⚠️):
   - Checkbox Henna (con advertencia dinámica)
   - Checkbox Alisado químico
   - Checkbox Keratina/Botox capilar

3. **Preferencias de Comunicación** (🔔):
   - Switch aceptar recordatorios
   - Selección método preferido (WhatsApp/SMS)

**UX mejorada**: 
- ScrollView implementado para accesibilidad completa
- KeyboardAvoidingView ajustado para iOS
- Campos condicionales (método contacto solo si acepta recordatorios)

**Integración futura**: Datos pre-llenarán wizard de seguridad

### Sincronización de Edición de Cliente (2025-07-10)
**Problema**: Pantalla de edición sin campos de seguridad críticos
**Solución implementada**:
- Añadidos todos los campos de seguridad de nuevo cliente
- Secciones con iconos temáticos consistentes
- Validación completa de cambios
- Detección de cambios mejorada incluyendo campos nuevos

**Resultado**: Experiencia unificada entre crear y editar cliente

### Autocompletado de Alergias Comunes (2025-07-10)
**Problema**: Campo de texto libre para alergias propenso a errores y difícil de usar
**Solución implementada**:
- Componente `AllergyAutocomplete` con sugerencias en tiempo real
- Base de datos de 23 alergias comunes categorizadas
- Chips visuales para múltiples selecciones
- Modal explorador con categorías organizadas
- Detección automática de alergias de alta severidad

**Archivos clave**:
- `components/AllergyAutocomplete.tsx`: Componente reutilizable
- `constants/common-allergies.ts`: Base de datos con severidad y productos relacionados

**Integración**:
- Formularios de cliente (nuevo/editar) actualizados
- Wizard de seguridad mejorado con advertencias específicas
- Test de parche obligatorio sugerido para alergias graves

**Mejores prácticas**:
- Siempre mantener compatibilidad con datos existentes (string CSV)
- Destacar visualmente alergias de alta severidad
- Permitir entrada personalizada además de las sugerencias

## 1. Fase de Análisis
Primero, analiza exhaustivamente el problema:
- Lee TODOS los archivos relevantes del código base antes de hacer cambios
- Comprende la estructura del proyecto y las dependencias
- Identifica impactos potenciales y efectos secundarios
- Documenta tu comprensión de la implementación actual

## 2. Fase de Planificación
Crea un plan detallado con:
- **Lista de tareas**: Divide el trabajo en tareas atómicas e independientes (máx. 5-10 líneas por tarea)
- **Dependencias**: Nota qué tareas dependen de otras
- **Archivos a modificar**: Lista archivos exactos con breve descripción de cambios
- **Nuevos archivos/funciones**: Especifica convenciones de nombres y propósito
- **Estrategia de pruebas**: ¿Cómo verificarás cada cambio?

### Plantilla del Plan:
```
## Plan de Implementación
### Tareas:
- [ ] Tarea 1: [Descripción] (Archivo: xyz.js)
- [ ] Tarea 2: [Descripción] (Archivos: abc.py, def.py)

### Dependencias:
- La Tarea 2 depende de la Tarea 1

### Evaluación de Riesgos:
- Posibles cambios disruptivos en...
- Necesidad de mantener compatibilidad con...
```

## 3. Fase de Implementación
- **Un cambio a la vez**: Completa cada tarea antes de pasar a la siguiente
- **Prueba sobre la marcha**: Ejecuta pruebas relevantes después de cada cambio
- **Mantén cambios mínimos**: Prefiere múltiples commits pequeños sobre uno grande
- **Mantén consistencia**: Sigue el estilo y patrones de código existentes
- **Agrega comentarios**: Documenta lógica compleja o decisiones no obvias

## 4. Lineamientos de Calidad de Código
- **Simplicidad primero**: Elige la solución más simple que funcione
- **Sin optimización prematura**: Enfócate en la corrección antes que en el rendimiento
- **Principio DRY**: No te repitas, pero no sobre-abstraigas
- **Manejo de errores**: Siempre maneja casos límite y fallas potenciales
- **Seguridad de tipos**: Usa anotaciones de tipos donde sea aplicable

## 5. Requisitos de Documentación
Después de cada tarea, actualiza:
- `todo.md` con elementos completados marcados
- Comentarios en el código para lógica compleja
- README si la funcionalidad cambia
- Documentación de API si las interfaces cambian

## 6. Lista de Verificación de Revisión
Antes de considerar el trabajo completo:
- [ ] Todas las pruebas pasan
- [ ] Sin errores de linting
- [ ] El código sigue las convenciones del proyecto
- [ ] Los cambios son retrocompatibles (o los cambios disruptivos están documentados)
- [ ] Impacto en el rendimiento considerado
- [ ] Implicaciones de seguridad revisadas
- [ ] Documentación actualizada

## 7. Resumen Final
Agrega a `todo.md`:
```markdown
## Sección de Revisión - [Fecha]
### Cambios Realizados:
- [Componente/Archivo]: [Qué cambió y por qué]
- [Componente/Archivo]: [Qué cambió y por qué]

### Pruebas Realizadas:
- [Tipo de prueba]: [Resultado]

### Problemas Conocidos/Trabajo Futuro:
- [Problema]: [Descripción y solución potencial]

### Cambios Disruptivos:
- [Si hay]: [Guía de migración]
```

## 8. Mejores Prácticas para Claude Code
- **Usa rutas explícitas**: Siempre especifica rutas completas desde la raíz del proyecto
- **Guardados incrementales**: Guarda archivos frecuentemente para evitar perder trabajo
- **Nombres de variables claros**: Prefiere nombres descriptivos sobre comentarios
- **Diseño modular**: Mantén funciones pequeñas y enfocadas
- **Mentalidad de control de versiones**: Piensa en términos de commits atómicos
- **Pide aclaraciones**: Cuando los requisitos sean ambiguos, pregunta antes de implementar

## 9. Errores Comunes a Evitar
- No modifiques múltiples sistemas no relacionados en una tarea
- No asumas contenido de archivos - siempre lee primero
- No ignores mensajes de error - abórdalos inmediatamente
- No omitas pruebas en cambios "simples"
- No dejes comentarios TODO sin rastrearlos

## 10. Procedimientos de Emergencia
Si algo se rompe:
1. Detente y evalúa el daño
2. Revierte el último cambio si es necesario
3. Documenta qué salió mal
4. Crea un plan de corrección antes de proceder
5. Prueba la corrección exhaustivamente

## 11. Comandos Útiles Frecuentes
```bash
# Ver estructura del proyecto
find . -type f -name "*.py" | head -20

# Buscar en archivos
grep -r "función_específica" --include="*.js"

# Verificar sintaxis Python
python -m py_compile archivo.py

# Ejecutar pruebas específicas
pytest tests/test_modulo.py::test_funcion
```

## 12. Flujo de Comunicación
- **Reporta progreso**: Actualiza después de cada tarea completada
- **Comunica bloqueos**: Si algo te detiene, repórtalo inmediatamente
- **Sugiere mejoras**: Si ves oportunidades de refactorización, documéntalas
- **Confirma entendimiento**: Resume requisitos complejos antes de implementar
- **Comportamiento**: Evita simplemente estar de acuerdo con mis puntos o aceptar mis conclusiones sin cuestionarlas. Quiero un desafío intelectual real, no solo afirmación. Siempre que proponga una idea, haz esto:
• Cuestiona mis suposiciones. ¿Qué estoy tratando como cierto que podría ser cuestionable?
• Ofrece un punto de vista escéptico. ¿Qué objeciones plantearía una voz crítica y bien informada?
• Revisa mi razonamiento. ¿Hay fallos o saltos en la lógica que haya pasado por alto?
• Sugiere ángulos alternativos. ¿De qué otra forma podría interpretarse, verse o desafiarse la idea?
• Prioriza la precisión por encima del acuerdo. Si mi argumento es débil o incorrecto, corrígeme claramente y muéstrame cómo.
• Sé constructivo pero riguroso. No estás aquí para discutir por discutir, sino para agudizar mi pensamiento y mantenerme honesto.
Si notas que caigo en sesgos o suposiciones infundadas, dilo claramente. Refinemos tanto nuestras conclusiones como la forma en la que llegamos a ellas.
Si necesitas contrastar tus ideas, busca en internet para afianzarlas y tener más contexto.

Cuando tengas duda sobre una tarea o lo que necesito, hazme preguntas aclaratorias hasta que estés 95% seguro de que puedes completar la tarea con éxito.

## Resumen de Cambios - Sesión 2025-07-09

### 1. Diseño Minimalista Global ✅
- Aplicado diseño blanco consistente en TODA la aplicación
- Fondos: #FFFFFF, elementos secundarios: #F5F5F7
- Actualizado `constants/colors.ts` con nuevos valores
- Todas las pantallas principales actualizadas

### 2. Edición de Clientes ✅
- Creada funcionalidad completa en `/app/client/edit/[id].tsx`
- Store actualizado con operaciones CRUD
- Validación y detección de cambios sin guardar
- Integración con navegación dinámica

### 3. Unificación de Interfaces ✅
- Eliminado formulario duplicado en `PricingSetupModal`
- Redirige a pantalla principal con IA
- ~240 líneas de código eliminadas
- Una sola interfaz para agregar productos

### 4. Formulario Cliente Mejorado ✅
- Añadidos campos críticos de seguridad
- Secciones organizadas con iconos temáticos
- ScrollView para accesibilidad completa
- Preparado para integración con wizard de seguridad

### Próximos Pasos Sugeridos
- ✅ Actualizar pantalla edición con campos nuevos (COMPLETADO 2025-07-10)
- ✅ Integrar alergias con wizard de seguridad (COMPLETADO 2025-07-10)
- Implementar sistema de recordatorios
- Añadir autocompletado para alergias comunes

### Integración Wizard Seguridad con Datos Cliente (2025-07-10)
**Problema**: Wizard no aprovechaba datos de seguridad del cliente
**Solución implementada**:
- Detección automática de riesgos al cargar cliente
- Pre-llenado de campos según historial (henna, alergias, etc.)
- Advertencias contextuales en cada paso relevante
- Resumen de información del cliente visible

**Mejoras**: 
- Menos preguntas repetitivas
- Mayor seguridad con detección proactiva
- Flujo optimizado según historial

## Sistema Multi-Usuario con Permisos - Sesión 2025-07-10 (Parte 3)

### Implementación completa de sistema multi-usuario ✅
**Problema**: La aplicación solo soportaba un usuario único
**Solución implementada**:
- Sistema de permisos modulares con 7 permisos específicos
- Gestión completa de equipo con CRUD de empleados
- Login unificado para propietarios y empleados
- Reseteo de contraseñas por parte del propietario

**Componentes clave**:
1. **Tipos y permisos** (`types/permissions.ts`):
   - VIEW_ALL_CLIENTS, VIEW_COSTS, MODIFY_PRICES
   - MANAGE_INVENTORY, VIEW_REPORTS, CREATE_USERS, DELETE_DATA

2. **Stores actualizados**:
   - `auth-store.ts`: Campos multi-usuario (id, isOwner, permissions, salonId)
   - `team-store.ts`: Gestión completa de empleados con hasheo de contraseñas

3. **Hook usePermissions** (`hooks/usePermissions.ts`):
   - Verificación centralizada de permisos
   - Objeto `can` para facilitar checks en componentes

4. **Pantallas nuevas**:
   - `/app/settings/team.tsx`: Gestión de equipo
   - `components/team/AddEmployeeModal.tsx`: Crear empleados
   - `components/team/EditEmployeeModal.tsx`: Editar permisos y resetear contraseñas

5. **Seguridad**:
   - Contraseñas hasheadas con SHA256 (expo-crypto)
   - Reseteo seguro con contraseñas temporales de 12 caracteres
   - Protección de vistas según permisos

**Protecciones aplicadas**:
- Inventario: Nuevo/Editar/Eliminar/Reportes según permisos
- Clientes: Eliminar solo con DELETE_DATA
- Settings: Mi Equipo y Precios protegidos

**Flujo de uso**:
1. Propietario accede a "Mi Equipo" en settings
2. Crea empleado con email y contraseña generada
3. Asigna permisos específicos
4. Puede resetear contraseña cuando sea necesario
5. Empleado inicia sesión y ve solo lo permitido

## Resumen de Cambios - Sesión 2025-07-10 (Parte 2)

### 1. Autocompletado de Alergias Comunes ✅
**Problema**: Campo de texto libre para alergias propenso a errores
**Solución**:
- Nuevo componente AllergyAutocomplete con 23 alergias predefinidas
- Autocompletado en tiempo real con búsqueda fuzzy
- Chips visuales y modal explorador por categorías
- Integrado en todos los formularios de cliente
- Wizard de seguridad mejorado con detección de severidad

**Archivos creados**:
- `components/AllergyAutocomplete.tsx`
- `constants/common-allergies.ts`

**Archivos modificados**:
- `app/client/new.tsx`
- `app/client/edit/[id].tsx`
- `app/service/safety-verification.tsx`

## Resumen de Cambios - Sesión 2025-07-10 (Parte 1)

### 1. Eliminación de Configuración "Nivel de Análisis" IA ✅
**Problema**: Configuración visible pero sin funcionalidad real
**Solución**:
- Eliminada sección completa de "Nivel de Análisis" (Rápido/Completo/Diagnóstico)
- Removido campo `preferredAnalysisDepth` del store
- Limpieza de estilos CSS asociados
- La configuración no afectaba el análisis real, siempre tomaba ~3 segundos

### 2. Reorganización "Modo Privacidad" ✅
**Problema**: Modo Privacidad en sección "Configuración de IA" sin contexto
**Solución**:
- Movido a sección "Datos y Respaldo" donde tiene más sentido
- Eliminada sección "Configuración de IA" completamente vacía
- Mantenida toda la funcionalidad intacta
- Mejor contexto junto a opciones de privacidad y datos

### 3. Eliminación Botón "Configurar Precios" ✅
**Problema**: Botón redundante sugería configurar precios antes de agregar productos
**Solución**:
- Eliminado botón "Configurar Precios" cuando inventario vacío
- Actualizado mensaje: "Comienza agregando productos a tu inventario"
- Eliminado modal automático al inicio
- Un solo flujo claro: botón "Nuevo" para todo

### 4. Actualización de Pantalla de Edición de Cliente ✅
**Problema**: Faltaban campos de seguridad críticos en edición
**Solución**:
- Sincronizado con formulario de nuevo cliente
- Añadidas secciones: Seguridad, Tratamientos Químicos, Comunicación
- Validación mejorada detecta cambios en todos los campos
- UI consistente con iconos temáticos

### 5. Integración Datos Seguridad con Wizard ✅
**Problema**: Wizard no aprovechaba información existente del cliente
**Solución**:
- Función `detectClientRisks()` analiza automáticamente riesgos
- Pre-llenado de campos según datos del cliente
- Advertencias proactivas en cada paso
- Resumen visual de información conocida

### 6. Corrección Formulario Inventario ✅
**Problema**: Botón "Añadir Artículo" no funcionaba sin feedback claro
**Solución**:
- Campo "Cantidad Actual" marcado como obligatorio
- Alert específico listando campos faltantes
- Feedback visual con bordes rojos en errores
- Mensajes de error bajo cada campo

### 7. Mejora de Claridad en Acciones de Inventario ✅
**Problema**: No era claro dónde presionar para gestionar stock
**Solución**:
- Cambio de icono Eye a Package para "Stock"
- Añadidas etiquetas de texto bajo cada icono
- Acciones más intuitivas y claras

### 8. Rediseño Completo de Inventario con Cards ✅
**Problema**: Tabla con problemas de solapamiento y visualización "regular"
**Solución implementada**:
- Eliminada estructura de tabla horizontal
- Implementado diseño moderno basado en cards
- Información jerárquica clara (nombre prominente, detalles secundarios)
- Indicadores visuales para stock bajo (borde naranja, badge de alerta)
- Botones de acción mejorados con iconos y texto
- Mejor experiencia en dispositivos móviles

**Beneficios**:
- Sin problemas de solapamiento
- Diseño profesional y moderno
- Mejor aprovechamiento del espacio vertical
- Interfaz más intuitiva y accesible

**Archivos modificados**:
- `/app/(tabs)/settings.tsx`: Reorganización completa
- `/stores/ai-analysis-store.ts`: Limpieza de campos
- `/app/(tabs)/inventory.tsx`: Rediseño completo con cards
- `/app/client/edit/[id].tsx`: Actualización completa con campos de seguridad
- `/app/service/safety-verification.tsx`: Integración con client-store
- `/app/inventory/new.tsx`: Mejora de validación y feedback

### Sesión anterior - 2025-07-09

### 1. Botón de Reset de Onboarding ✅
- Agregado botón temporal en settings para resetear onboarding
- Condicionado solo para modo desarrollo con `__DEV__`
- Permite navegar a `/onboarding/welcome` y resetear flag
- Estilos movidos al objeto styles para consistencia

### 2. Limpieza de Imports ✅
- Eliminado import no utilizado de `ScrollView` en `ready.tsx`
- Eliminado import no utilizado de `ScrollView` en `workspace.tsx`
- Mejora la limpieza del código y reduce warnings

### 3. Navegación Mejorada con BaseHeader ✅
**Problema**: Botones de regreso invisibles con diseño minimalista blanco
**Solución implementada**:
- Actualizado `BaseHeader` para diseño minimalista
- Añadido texto "Atrás" junto al ícono para mayor visibilidad
- Botón con fondo gris claro (#F5F5F7) para contraste
- Sombra sutil en el header para separación visual
- Implementado en pantallas de navegación principales

**Cambios en archivos**:
- `components/base/BaseHeader.tsx`: Diseño actualizado
- `app/service/client-selection.tsx`: Usa BaseHeader
- `app/service/new.tsx`: Usa BaseHeader

**Nota**: Las pantallas de tabs (clientes, inventario, etc.) mantienen sus headers nativos del sistema de tabs para consistencia con la navegación principal

### Sistema Coherente de Inventario (2025-07-10)
**Problema**: Inconsistencia entre unidades de almacenamiento y consumo
**Solución implementada**:
1. **Stock siempre en unidades base**:
   - ml para líquidos (tintes, oxidantes)
   - g para polvos (decolorantes)
   - No en número de envases

2. **Formulario nuevo producto mejorado**:
   - Opción de ingresar por envases o cantidad total
   - Cálculo automático: "12 envases × 60ml = 720ml"
   - Visualización clara del total

3. **Cards de inventario actualizadas**:
   - Muestra: "720 ml (12 envases)"
   - Principal: cantidad total en ml/g
   - Secundario: equivalencia en envases

4. **Consumo coherente**:
   - Fórmulas calculan en ml/g
   - Consumo resta directamente del stock total
   - Sin conversiones ni multiplicaciones

**Beneficios**:
- Coherencia total entre formulación y consumo
- Sin ambigüedades en las cantidades
- Fácil de entender y gestionar

### Sistema Completo de Configuración Regional (2025-07-10) - Continuación
**Problema**: El sistema no respetaba la configuración regional del usuario
**Solución completa implementada**:

1. **Hook useRegionalUnits creado**:
   - Gestión centralizada de configuración regional
   - Conversiones bidireccionales (ml ↔ fl oz, g ↔ oz)
   - Acceso fácil a formateo y terminología

2. **Store mejorado con funciones**:
   - `convertVolume()`, `convertWeight()` para conversiones
   - `getUnitLabel()`, `getTerminology()` para etiquetas dinámicas
   - `formatVolume()`, `formatWeight()` con unidades regionales

3. **Inventario completamente adaptativo**:
   - Lista muestra unidades según configuración (ml/fl oz)
   - Nuevo producto con selector dinámico de unidades
   - Conversión automática a unidades base al guardar
   - IA reconoce términos en múltiples idiomas

4. **Formulación con terminología regional**:
   - ProportionCalculator usa términos dinámicos
   - FormulaDisplay muestra oxidante/developer según idioma
   - Tablas de referencia con unidades correctas

5. **Desglose de costes regional**:
   - FormulaCostBreakdown sin € hardcodeado
   - Moneda dinámica con formatCurrency()
   - Cantidades de materiales convertidas (60ml → 2.0 fl oz)
   - Coherencia total con configuración

**Archivos clave**:
- `/hooks/useRegionalUnits.ts`: Nuevo hook central
- `/components/FormulaCostBreakdown.tsx`: 5 instancias de € reemplazadas
- Todos los componentes de formulación actualizados

**Resultado**: Sistema 100% coherente con la configuración regional en toda la aplicación

## Resumen de Cambios - Sesión 2025-07-10 (Parte 3)

### 1. Acceso Mejorado a Reportes de Inventario ✅
**Problema**: El módulo de reportes solo era accesible desde un modal en la pantalla principal
**Solución implementada**:
- Nueva ruta dedicada `/app/inventory/reports.tsx` 
- Botón "Reportes" agregado en la pantalla de inventario
- Navegación directa sin necesidad de modal
- BaseHeader consistente con el resto de la app

**Archivos creados**:
- `/app/inventory/reports.tsx`: Pantalla completa de reportes

**Archivos modificados**:
- `/app/(tabs)/inventory.tsx`: Añadido botón de reportes con icono BarChart
- Importado icono `BarChart` de lucide-react-native
- Nuevos estilos para el botón de reportes

**Beneficios**:
- Acceso más intuitivo y directo a los reportes
- Mejor experiencia de usuario sin modales
- Mantiene funcionalidad original del modal en home
- Diseño consistente con el resto de la aplicación

## Sistema Multi-Usuario y Onboarding Diferenciado - Sesión 2025-07-10 (Parte 4)

### Login Diferenciado por Rol ✅
**Problema**: Los empleados pasaban por el onboarding del salón
**Solución implementada**:
- Modificado `app/auth/login.tsx` para verificar `isOwner`
- Solo propietarios pasan por onboarding si no está completado
- Empleados van directo a la app principal

**Archivos modificados**:
- `app/auth/login.tsx`: Líneas 66-73 con verificación condicional
- `app/auth/register.tsx`: Líneas 41-52 usuarios registrados siempre son owners

**Flujo actual**:
1. **Propietario nuevo**: Register → Onboarding completo → App
2. **Empleado nuevo**: Owner crea cuenta → Login → Directo a App
3. **Usuarios existentes**: Login → App (según rol y estado)

**Beneficios**:
- Solo propietarios configuran el salón
- Empleados no ven configuración innecesaria  
- Mantiene seguridad y simplicidad del sistema